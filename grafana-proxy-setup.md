# Grafana 免密登录代理配置指南

## 概述
本配置实现了通过 nginx 代理访问 Grafana 的免密登录功能，同时保持直接访问 Grafana 的能力。

## 1. Docker Compose 配置

修改你的 `docker-compose.yml` 文件，添加以下环境变量：

```yaml
version: "3.9"

services:
  grafana:
    image: grafana/grafana:10.2.3
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # 基本配置 - 重要：root_url 必须包含完整的子路径
      GF_SERVER_DOMAIN: **************
      GF_SERVER_HTTP_PORT: 3000
      GF_SERVER_ROOT_URL: http://**************:30009/grafana/
      GF_SERVER_SERVE_FROM_SUB_PATH: "true"
      GF_SECURITY_ALLOW_EMBEDDING: "true"
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_DEFAULT_INSTANCE_TIMEZONE: Asia/Shanghai
      
      # 免密登录配置
      GF_AUTH_PROXY_ENABLED: "true"
      GF_AUTH_PROXY_HEADER_NAME: "X-WEBAUTH-USER"
      GF_AUTH_PROXY_HEADER_PROPERTY: "username"
      GF_AUTH_PROXY_AUTO_SIGN_UP: "true"
      GF_AUTH_PROXY_ENABLE_LOGIN_TOKEN: "false"
      GF_AUTH_PROXY_HEADERS: "Name:X-WEBAUTH-NAME Email:X-WEBAUTH-EMAIL"
      GF_USERS_ALLOW_SIGN_UP: "true"
      GF_USERS_AUTO_ASSIGN_ORG: "true"
      GF_USERS_AUTO_ASSIGN_ORG_ID: "1"
      GF_USERS_AUTO_ASSIGN_ORG_ROLE: "Admin"
      
    volumes:
      - /home/<USER>/grafana/data:/var/lib/grafana
    user: "472"
```

## 2. Nginx 配置

在 nginx.conf 中添加以下配置：

```nginx
# Grafana 免密登录代理
location /grafana/ {
    # 不重写 URL，保持 /grafana/ 路径
    
    # 设置免密登录的用户信息
    proxy_set_header X-WEBAUTH-USER "admin";
    proxy_set_header X-WEBAUTH-NAME "Admin User";
    proxy_set_header X-WEBAUTH-EMAIL "<EMAIL>";
    
    # 基本代理设置
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;
    
    # WebSocket 支持
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_http_version 1.1;
    
    # 超时设置
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;
    
    # 代理到 Grafana - 保持 /grafana/ 路径
    proxy_pass http://**************:3000/grafana/;
    
    # 禁用缓存
    proxy_cache off;
    proxy_buffering off;
}

# Grafana API 代理（用于 render-image 等 API 调用）
location /grafana-api/ {
    # 重写 URL，去掉 /grafana-api 前缀
    rewrite ^/grafana-api(/.*)?$ $1 break;
    
    # API 调用使用 Bearer Token 认证
    set $grafana_token "glsa_vrwAo1hIRT4RevKcm3yIzHcbOE7ejF4J_794dde82";
    proxy_set_header Authorization "Bearer $grafana_token";
    
    # 基本代理设置
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 代理到 Grafana
    proxy_pass http://**************:3000;
    
    # 禁用缓存
    proxy_cache off;
}
```

## 3. 前端代码修改

已修改前端代码中的 iframe 地址，使用相对路径通过 nginx 代理访问：

```javascript
// 修改前
const defaultUrl = "http://**************:3000/d/...";

// 修改后
const defaultUrl = "/grafana/d/...";
```

## 4. 访问方式

配置完成后，你可以通过以下两种方式访问 Grafana：

### 免密登录访问（通过 nginx 代理）
- URL: `http://your-nginx-server/grafana/`
- 特点: 自动以 admin 用户身份登录，无需输入密码

### 直接访问（需要登录）
- URL: `http://**************:3000`
- 特点: 需要手动输入用户名和密码登录

## 5. API 调用

对于需要 API Token 的调用（如 render-image），使用 `/grafana-api/` 路径：

```javascript
// 示例：获取面板图片
const response = await fetch('/grafana-api/render/d-solo/dashboard-uid/panel-name?panelId=1&width=800&height=400');
```

## 6. 部署步骤

1. 停止现有的 Grafana 容器：
   ```bash
   docker-compose down
   ```

2. 更新 docker-compose.yml 文件

3. 更新 nginx.conf 文件

4. 重启服务：
   ```bash
   docker-compose up -d
   nginx -s reload
   ```

## 7. 验证配置

1. 访问 `http://your-nginx-server/grafana/` 应该自动登录
2. 访问 `http://**************:3000` 应该显示登录页面
3. 前端页面中的 iframe 应该正常显示 Grafana 面板

## 注意事项

- 确保 Grafana Token 有效且具有足够权限
- 免密登录的用户信息可以根据需要修改
- 如果需要不同的用户权限，可以修改 `GF_USERS_AUTO_ASSIGN_ORG_ROLE` 环境变量
- 建议在生产环境中使用 HTTPS
