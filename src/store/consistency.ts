import { defineStore } from "pinia";
import { ref } from "vue";

/**
 * 一致性测试相关的状态管理
 */
const useConsistencyStore = defineStore("consistency", () => {
  // 当前选中的任务记录
  const currentRecord = ref<any>(null);
  
  // 设置当前记录
  function setCurrentRecord(record: any) {
    currentRecord.value = record;
  }
  
  // 清除当前记录
  function clearCurrentRecord() {
    currentRecord.value = null;
  }
  
  // 获取当前记录
  function getCurrentRecord() {
    return currentRecord.value;
  }

  return {
    currentRecord,
    setCurrentRecord,
    clearCurrentRecord,
    getCurrentRecord,
  };
});

export default useConsistencyStore;
