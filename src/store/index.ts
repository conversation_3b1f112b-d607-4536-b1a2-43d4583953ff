import useAuthorityStore from "./authority";
import useMenuStore from "./menu";
import useApiStore from "./api";
import useWordStore from "./word";
import useCommonStore from "./common";
import useConsistencyStore from "./consistency";
// 统一导出useStore方法
export default function useStore() {
  return {
    authority: useAuthorityStore(),
    menu: useMenuStore(),
    api: useApiStore(),
    word: useWordStore(),
    common:useCommonStore(),
    consistency: useConsistencyStore(),
  };
}
