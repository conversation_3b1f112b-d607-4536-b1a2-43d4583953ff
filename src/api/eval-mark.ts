import axios from "./axios";
import { dataC } from "turing-plugin";
const markCategoryBaseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/mark-category`;
const markRecordBaseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/mark-record`;

//创建标注记录
export function createMarkRecord(data: any) {
  const url = markRecordBaseUrl + `/_create`;
  return axios.post(url, data);
}

// 根据markRecordId获取标注记录
export function getMarkReocordById(markRecordId: string, params: any) {
  const url = markRecordBaseUrl + `/item/${markRecordId}`;
  return axios.post(url, params);
}

// 查询未完成的标注记录
export function findUnfinished(missionId: string) {
  const url = markRecordBaseUrl + `/unfinished`;
  const params: any = {};
  if (!dataC.isEmpty(missionId)) params.missionId = missionId;
  console.log("params", params);
  return axios.get(url, { params });
}

//删除指定标注记录
export function deleteMarkResult(id: String) {
  const url = markRecordBaseUrl + `/by-result/` + id;
  return axios.delete(url);
}

//标注模式接口
export function getMarkCategoryList() {
  const url = markCategoryBaseUrl + `/list`;
  return axios.get(url);
}

export function doAscribe(taskId: String) {
  const url = markRecordBaseUrl + `/ascribe/${taskId}`;
  return axios.post(url, {});
}

export function doSave(data: any, missionId: String, completeCheck: boolean) {
  const url = markRecordBaseUrl + `/save`;
  return axios.post(url, { ...data, missionId, completeCheck });
}
