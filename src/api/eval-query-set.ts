import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/query-group`

// 新建业务应用
export function createQueryGroup(data: any): Promise<any> {
  const url = `${baseUrl}/create`;
  return axios.post(url, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 编辑query集
export function updateQueryGroup(id: string, data: any): Promise<any> {
  const url = `${baseUrl}/update/${id}`;
  return axios.put(url, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 发布query集
export function publishQueryGroup(id: string): Promise<any> {
  const url = `${baseUrl}/publish/${id}`;
  return axios.get(url);
}

// 分页查询query集
export function getQueryGroupsPage(params: any): Promise<any> {
  const url = `${baseUrl}/page`;
  return axios.get(url, { params });
}

// 获取数据集分组列表
export function getLabelList(): Promise<any> {
  const url = `${baseUrl}/label/list`;
  return axios.get(url);
}

// 下载模板
export function downloadTemplate(data:any): Promise<any> {
  const url = `${baseUrl}/download/template`;
  return axios.get(url, {
    params:data,
    responseType: "blob",
  });
}

// 查询query集明细
export function getQueryGroupDetail(id: string,data:any): Promise<any> {
  const url = `${baseUrl}/detail/${id}`;
  return axios.get(url,{params:data});
}

// 删除query集
export function deleteQueryGroup(id: string): Promise<any> {
  const url = `${baseUrl}/delete/${id}`;
  return axios.delete(url);
}

export function publishList(): Promise<any> {
  const url = `${baseUrl}/publishList`;
  return axios.get(url);
}

// 导入query集离线数据
export function importOfflineData(data: any): Promise<any> {
  const url = `${baseUrl}/import-offline`;
  return axios.post(url, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

//下载离线数据模版
export function downloadOfflineTemplate(data:any): Promise<any> {
  const url = `${baseUrl}/download/template`;
  return axios.get(url, {
    params:data,
    responseType: "blob",
  });
}
//导出
export function exportQueryGroup(id: string): Promise<any> {
  const url = `${baseUrl}/detail/export/${id}`;
  return axios.get(url, {
    responseType: "blob",
  });
}