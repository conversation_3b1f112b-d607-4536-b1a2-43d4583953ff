/*
 * @Description: 一致性测试任务管理接口
 */

import axios from './axios';

// 接口基础路径
const BASE_URL = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/consistency-mission`;

/**
 * 一致性测试任务创建/更新DTO
 */
export interface ConsisMissionCreateDTO {
  id?: string;
  name: string;
  description?: string;
  querySetId: string;
  strategyIds: string[];
  regionIds: string[];
  [key: string]: any;
}

/**
 * 一致性测试任务查询DTO
 */
export interface ConsisMissionQueryDTO {
  name?: string;
  status?: number;
  createUser?: string;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
}

/**
 * 一致性测试任务删除DTO
 */
export interface ConsisMissionDeleteDTO {
  ids: string[];
}

/**
 * 一致性对比结果查询DTO
 */
export interface ConsisVsResultQueryDTO {
  missionId: string;
  query?: string;
  type?: number;
  nodeCode?: string;
}

/**
 * 任务进度响应
 */
export interface MissionProgressResponse {
  missionId: string;
  status: number;
  progress: number;
  totalCount: number;
  completedCount: number;
  failedCount: number;
  startTime?: string;
  endTime?: string;
  [key: string]: any;
}

/**
 * 一致性比对结果响应
 */
export interface ConsistencyResultResponse {
  missionId: string;
  totalQueries: number;
  consistentQueries: number;
  inconsistentQueries: number;
  consistencyRate: number;
  details: any[];
  [key: string]: any;
}

/**
 * 创建或更新一致性测试任务
 * @param data 任务创建/更新数据
 * @returns Promise<any>
 */
export function saveConsisMission(data: ConsisMissionCreateDTO): Promise<any> {
  const url = `${BASE_URL}/save`;
  return axios.post(url, data);
}

/**
 * 复制一致性测试任务
 * @param missionId 任务ID
 * @returns Promise<any>
 */
export function copyConsisMission(missionId: string): Promise<any> {
  const url = `${BASE_URL}/copy/${missionId}`;
  return axios.post(url);
}

/**
 * 分页查询一致性测试任务
 * @param queryDTO 查询条件
 * @returns Promise<any>
 */
export function pageConsisMission(queryDTO: ConsisMissionQueryDTO): Promise<any> {
  const url = `${BASE_URL}/page?page=${queryDTO.page}&size=${queryDTO.size}&sort=${queryDTO.sort}`;
  return axios.post(url, queryDTO);
}

/**
 * 批量删除一致性测试任务
 * @param deleteDTO 删除数据
 * @returns Promise<any>
 */
export function deleteConsisMission(deleteDTO: ConsisMissionDeleteDTO): Promise<any> {
  const url = `${BASE_URL}/delete`;
  return axios.post(url, deleteDTO);
}

/**
 * 启动一致性测试任务
 * @param missionId 任务ID
 * @returns Promise<any>
 */
export function startConsisMission(missionId: string): Promise<any> {
  const url = `${BASE_URL}/start/${missionId}`;
  return axios.get(url);
}

/**
 * 获取任务进度
 * @param missionId 任务ID
 * @returns Promise<MissionProgressResponse>
 */
export function getMissionProgress(missionId: string): Promise<any> {
  const url = `${BASE_URL}/progress/${missionId}`;
  return axios.get(url);
}

/**
 * 查看一致性比对结果
 * @param missionId 任务ID
 * @returns Promise<ConsistencyResultResponse>
 */
export function getConsistencyResult(missionId: string): Promise<any> {
  const url = `${BASE_URL}/result/${missionId}`;
  return axios.get(url);
}

/**
 * 分页查询一致性对比异常结果
 * @param queryDTO 查询条件
 * @param pageable 分页参数
 * @returns Promise<any>
 */
export function pageVsResult(queryDTO: ConsisVsResultQueryDTO, pageable?: any): Promise<any> {
  const url = `${BASE_URL}/vs-result/page?page=${pageable.page}&size=${pageable.size}&sort=${pageable.sort}`;
  return axios.post(url, queryDTO);
}

/**
 * 导出一致性对比异常结果
 * @param queryDTO 查询条件
 * @returns Promise<Blob>
 */
export function exportVsResult(queryDTO: ConsisVsResultQueryDTO): Promise<any> {
  const url = `${BASE_URL}/vs-result/export`;
  return axios.post(url, queryDTO, {
    responseType: "blob",
  });
}

/**
 * 获取流程中关注节点的列表
 * @param queryDTO 查询条件
 * @returns Promise<Blob>
 */
export function concernNodeList(processId: String): Promise<any> {
  const url = `${BASE_URL}/node-list/${processId}`;
  return axios.get(url);
}
