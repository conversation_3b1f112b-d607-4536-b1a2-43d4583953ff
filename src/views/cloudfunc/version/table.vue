<template>
  <div class="cloudfunc-table">
    <AddDialog ref="addRef" @reload="loadList" />
    <scriptDialog ref="scriptDialogRef" />
    <table-page
      ref="myTableRef"
      name="cloudfunc-table"
      :columns="columns"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :defaultSort="{ prop: 'version', order: 'desc' }"
      operationAuth="/base/#/cloudfunc/edit"
      :operations="operations"
      @operation="handleOperation"
    >
    </table-page>
    <el-dialog
      v-model="dialogVisible"
      title="请选择同步环境"
      width="500px"
      @close="dialogVisible = false"
    >
      <div>
        <my-select v-model="region" :options="regionOptions" />
        <div class="footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="events.syncSubmit">
            确定
          </el-button>
        </div>
      </div>
    </el-dialog>
    <DebugCode ref="debugCodeRef" :ruleForm="currentRecord" />
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, nextTick, onMounted, onUnmounted, computed } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as cloudfuncApi from "@/api/cloudfunc";
import * as util from "@/utils/common";
import AddDialog from "./add.vue";
import ScriptDialog from "./scriptDialog.vue";
import useStore from "@/store";
import DebugCode from "./debug.vue";
const { word } = useStore();
const props = defineProps(["datasetId", "datasetName"]);

const { $app, proxy, $auth } = useCtx();
const routeQuery = computed(() => $app.$route.query);
const dialogVisible = ref(false);
const region = ref<string>("");
const regionOptions = ref<any[]>([]);
const currentRecord = ref<any>({});
const noSyncBtn = routeQuery.value.envType!=1||routeQuery.value.typeRender=="数据去重"
//列配置
const columns = ref([
  {
    prop: "nameRender",
    label: "名称",
    width: 250,
  },
  {
    prop: "desc",
    label: "描述",
    minWidth: 200,
  },
  {
    prop: "language",
    label: "脚本语言",
    width: 160,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.script(record);
      },
    },
    sortable: false,
  },
  {
    prop: "enabled",
    label: "是否启用",
    width: 150,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        $app
          .$confirm({
            title: `您确认要${record.enabled ? "停用" : "启用"}云函数版本"${
              record.name
            }"吗？`,
          })
          .then((res) => {
            cloudfuncApi
              .updateMetaFuncVersion(routeQuery.value.region, {
                ...record,
                enabled: !record.enabled,
              })
              .then((res) => {
                loadList();
                $app.$message.success(
                  record.enabled ? "云函数版本停用成功" : "云函数版本启用成功"
                );
              });
          });
      },
    },
  },
  // { prop: "publishTsRender", label: "发布时间", width: 180 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width:noSyncBtn? 249:296, fixed: "right" },
]);
//列表查询
const loadListData = (data: any) => {
  const { page, size } = data;
  return new Promise((resolve: any) => {
    cloudfuncApi
      .getMetaFuncVersionList(routeQuery.value.region, {
        ...data,
        scriptId: routeQuery.value.scriptId,
      })
      .then((result) => {
        //返回数据
        resolve(result);
      });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.versionRender = util.padNumberToDigits(x.version, 3);
    x.nameRender = `${x.name}(v${x.versionRender})`;
    x.siteCount = `${
      !dataC.isEmpty(x.siteVersionIds) ? x.siteVersionIds.length : 0
    }个站点`;
    x.publishTsRender = timeC.format(x.publishTs, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(
      x.lastModifiedDate,
      "YYYY-MM-DD hh:mm:ss"
    );
    return x;
  });
};
//操作
const operations = [
{
    type: "debug",
    label: "调试",
  },
  {
    type: "edit",
    label: "编辑",
    //只有草稿状态的版本可编辑
    disabled: (record: any) => record.enabled,
    disabledTips: "已经启用，不可编辑",
  },

  { type: "copy", label: "复制版本" },
  {
    type: "sync",
    label: "同步",
    btnType: "primary",
    disabled: (record: any) => !record.enabled,
    disabledTips: (record: any)=>{
      if(!record.enabled){
        return "未启用，不能同步"
      }

      return ""
    },
    hidden: () => noSyncBtn,
  },
  {
    type: "delete",
    label: "删除版本",
    btnType: "danger",
    disabled: (record: any) => record.enabled,
    disabledTips: "已经启用，不能删除",
  },
];
async function getAreaOptions() {
  const res: any = await word.getAreaList();
  regionOptions.value = res.map((item) => ({
    label: item.name,
    value: item.code,
  })).filter((item:any)=>item.value!==routeQuery.value.region)
  region.value = regionOptions.value[0].value;
}
getAreaOptions();
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  script: (record: any) => {
    proxy.$refs.scriptDialogRef?.openDialog({ ...record });
  },
  debug: (record: any) => {
    currentRecord.value = record;
    nextTick(()=>{
      proxy.$refs.debugCodeRef.debugData.debugCodeClick(currentRecord.value.inputArgs);
    })
  },
  add: (record: any) => {
    proxy.$refs.addRef?.openDialog("add", record);
  },
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  copy: (record: any) => {
    $app.$confirm({ title: `确定复制 ${record.nameRender}?` }).then(() => {
      cloudfuncApi
        .copyMetaFuncVersion(routeQuery.value.region, record.id)
        .then((result) => {
          loadList();
          $app.$message.success(`复制 ${record.nameRender} 成功`);
        });
    });
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.nameRender}?`,
      })
      .then(() => {
        cloudfuncApi
          .deleteMetaFuncVersion(routeQuery.value.region, record.id)
          .then((result) => {
            loadList();
            $app.$message.success(`删除 ${record.nameRender} 成功`);
          });
      });
  },
  sync: (record: any) => {
    dialogVisible.value = true;
    currentRecord.value = record;
  },
  syncSubmit: () => {
    cloudfuncApi.syncMetaFuncVersion(routeQuery.value.region, {versionId:currentRecord.value.id,targetRegion:region.value}).then((result) => {
      loadList();
      $app.$message.success(`同步 ${currentRecord.value.nameRender} 成功`);
      dialogVisible.value = false;
    });
  },
});
const openWindow = (type: string, item: any) => {
  events[type](item);
};
const loadList = () => {
  proxy.$refs["myTableRef"].loadData();
};
//初始化
onMounted(() => {});
//销毁
onUnmounted(() => {});
//事件声明
const emit = defineEmits(["edit-data"]);
//接口暴露
defineExpose({
  loadList,
  openWindow,
});
</script>
<style lang="scss">
.cloudfunc-table {
  height: calc(100% - 40px);

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}

.footer {
  margin-top: 5px;
  display: flex;
  justify-content: flex-end;
}
</style>
