<template>
  <my-drawer class="component-add" v-model="dialogVisible" :title="dialogTitle" :width="700" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :label-width="120" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" />
    <template #extraOperate>
      <el-button type="primary" @click="handleConfirm(true)" :disabled="rowData.status == 2">确认并进入画布</el-button>
    </template>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import { addTraffic, editTraffic } from "@/api/traffic.ts";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import { getOpenedProductList } from "@/api/product.ts";

const { t } = useI18n();
const { $app, $router } = useCtx();

const emits = defineEmits(["reload", "canvas"]);
const props = defineProps({
  productList: { type: Array, default: [] }, // 按钮类型
  activeId: { type: String, default: [] },
});
const envType = computed(() => {
  //2代表线上环境 1代表验证环境
  return $router.currentRoute.value.name == "traffic" ? 2 : 1;
});
const dialogTitle = computed(() => {
  return (formType.value === "add" ? t("btn.new") : t("btn.edit")) + "分流版本";
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 获取产品列表
const productList = ref([]);
const rowData = ref({});
const getProducts = () => {
  getOpenedProductList().then((res: any) => {
    productList.value = res?.data?.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  });
};

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = (canvas: boolean = false) => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editTraffic : addTraffic;
      func(ruleForm.value).then((res) => {
        if (!canvas) {
          $app.$message.success(isUpdate.value ? "分流版本编辑成功" : "分流版本新建成功");
        } else {
          if (!isUpdate.value) ruleForm.value.id = res.data;
          emits("canvas", ruleForm.value);
        }
        dialogVisible.value = false;
        emits("reload");
      });
    }
  });
};

/* 校验 */
const { validateNameRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  name: "",
  description: "",
  productId: "",
  envType: envType.value,
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [
    { required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, "请输入分流版本名称") },
  ],
  productId: [{ required: true, trigger: "change", message: "请选择所属产品" }],
});
// 表单项
const formItems = ref<any>({
  productId: {
    label: "所属产品",
    type: "select",
    options: computed(() => props.productList),
    disabled: () => {
      return props.activeId !== "all" || isUpdate.value;
    },
  },
  name: {
    label: "分流版本名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: NAME_RULE,
    },
  },
  description: {
    label: "分流版本描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});

const isHasProduct = ref(false);
const openDialog = async (type: string, row: any) => {
  rowData.value = row;
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      !row && getProducts();
      isHasProduct.value = !!row;
      ruleForm.value = assign({}, defaultForm, row);
    }
  });
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
