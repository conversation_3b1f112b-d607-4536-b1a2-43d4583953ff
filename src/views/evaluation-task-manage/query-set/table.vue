<template>
  <div class="query-set-table">
    <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
    <table-page ref="myTableRef" name="query-set-table" :columns="columns" :query="query" :loadDataApi="loadListData"
      :transformListData="transformListData" operationAuth="/base/#/query-set/edit" :operations="operations"
      @operation="handleOperation">
      <template #description="scope">
        <el-button link type="primary" @click="events.openDescriptionEditWindow(scope.row)" :disabled="!testAuth()">
          <el-icon>
            <Edit />
          </el-icon>
        </el-button>
        {{ scope.row.description }}
      </template>
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search"
            @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" :disabled="!testAuth()">创建query集</my-button>
            </template>
          </my-operation>
        </div>
      </template>

    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { keys, assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as querySetApi from "@/api/eval-query-set";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";
const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/query-set/edit");
};
const statusEnum = [
  { value: 1, label: "草稿", type: 'info' },
  { value: 2, label: "已发布", type: 'success' },
  { value: 3, label: "已归档", type: 'warning' },
];
const { api, common } = useStore();
const props = defineProps({
});
//列配置
const columns = ref([
  {
    prop: "name",
    label: "query集名称",
    width: 180,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.statistic(record);
      },
    },
  },
  { prop: "description", label: "query集描述", width: 180, slotName: "description"  },
  {
    prop: "label",
    label: "分组",
    minWidth: 120,
  },

  {
    prop: "size",
    label: "条数",
    width: 120,
  },
  {
    prop: "status",
    label: "状态",
    width: 150,
    custom: "status",
    customRender: {
      options: statusEnum
    },
  },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "operation", label: "操作", width: 160, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  search: "",
});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    attrs: {
      placeholder: "名称 或 分组",
    },
  },
});

//列表查询
const loadListData = async (data: any) => {
  return new Promise((resolve: any) => {
    querySetApi.getQueryGroupsPage(data).then((result) => {
      result.content = result.content.map((item) => ({
        ...item,
      }));
      //返回数据
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => record.status == 2,
    disabledTips: (record) => {
      if (record.status == 2) {
        return "版本已经发布，不可编辑";
      }
    },
  },
  {
    type: "publish",
    label: "发布",
    disabled: (record: any) => record.status != 1,
    disabledTips: (record) => {
      if (record.status == 2) {
        return "版本已经发布，不可发布版本";
      }
    },
  },
  {
    type: "delete", label: "删除", btnType: "danger",
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  modifyDescription(data: any, record: any) {
    querySetApi.updateQueryGroup(record.id, { ...record, description: data.description }).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  statistic: (record: any) => {
    emit("statistic-data", record);
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  add: () => {
    emit("edit-data-inst", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data-inst", "edit", record);
  },
  publish: (record: any) => {
    querySetApi.publishQueryGroup(record.id).then((res) => {
      $app.$message.success(`${record.name}发布成功`);
      loadList();
    });
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        querySetApi.deleteQueryGroup(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
});
const modelValue = reactive({
  metaRegionList: [],
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//初始化
onMounted(() => { });
//销毁
onUnmounted(() => {
});
//事件声明
const emit = defineEmits(["edit-data-inst", "statistic-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.query-set-table {
  height: 100%;
}
</style>