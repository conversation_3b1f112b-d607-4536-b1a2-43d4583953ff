<template>
  <my-drawer class="mock-add" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import * as metaWordApi from "@/api/meta-word";

import type { FormRules } from "element-plus";
import { downloadFile, findNodeById } from "@/utils/common.ts";
import useValidate from "@/hooks/validate";
import useStore from "@/store";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const { t } = useI18n();
const { word } = useStore();
const props = defineProps({
  treeNode: { type: Object, default: {} },
  columns: { type: Object, default: [] },
  treeData: { type: Object, default: [] },
});
const dialogTitle = computed(() => {
  return formType.value === "add" ? t("btn.new") : t("btn.edit");
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

/* 校验 */
const { validateNameRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  nodeId: undefined,
  word: "",
  relationWords: "",
  mutualRelationWords: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  word: [
    {
      required: true,
      trigger: "blur",
      message: "请输入query",
    },
  ],
  relationWords: [{ required: true, message: "请输入", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>();

const getOptions = () => {
  const parent = findNodeById(props.treeData, props.treeNode.parentId);
  formItems.value = {
    word: {
      label: "原词",
      type: "input",
      attrs: {
        maxlength: 50,
        placeholder: "请输入原词,比如搜搜地图",
      },
    },
    relationWords: {
      label:
        props.treeNode.name !== "同义词" && parent?.name !== "同义词"
          ? props.treeNode.name == "纠错词" || parent?.name == "纠错词"
            ? "纠错词"
            : ""
          : "单向同义词",
      type: "textarea",
      attrs: { maxlength: props.treeNode.name !== "同义词" && parent?.name !== "同义词" ? 200 : 500 },
      hidden: () => {
        return props.columns.findIndex((item: any) => item.prop == "relationWords") == -1;
      },
    },
    mutualRelationWords: {
      label: "双向同义词",
      type: "textarea",
      attrs: { maxlength: 500 },
      hidden: () => {
        return props.treeNode.name !== "同义词" && parent?.name !== "同义词";
      },
    },
  };
};

const openDialog = async (type: string, row: any) => {
  console.log(type, row);
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 2. 加载相关的下拉列表数据
  getOptions();
  // 3. 回显相关的操作
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      ruleForm.value = assign(defaultForm, { nodeId: props.treeNode.id });
    }
  });
};
const emits = defineEmits(["save-data"]);
const submit = (form: any) => {
  // 接口相关业务代码，执行完成后关闭弹窗
  if (isUpdate.value) {
    metaWordApi.editDic(form.id, form, word.area).then(() => {
      $app.$message.success("修改成功");
      emits("save-data", props.treeNode.id);
      handleClose();
    });
  } else {
    metaWordApi.addDic(form, word.area).then(() => {
      $app.$message.success("新增成功");
      emits("save-data");
      handleClose();
    });
  }
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
