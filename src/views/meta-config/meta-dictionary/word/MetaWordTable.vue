<template>
  <div class="meta-dict-table" v-show="treeNode?.id">
    <table-page
      ref="myWordTableRef"
      :name="treeNode?.id"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operations="operations"
      @operation="handleOperation"
      :loadImmediately="false"
      :withPagination="false"
      :withSort="false"
      :withSelection="true"
      operationAuth="/base/#/meta-word/edit"
      :dragRow="!(treeNode?.children && treeNode?.children.length) && dragable"
      @row-change="handleTableRowChange"
      @selection-change="handleTableSelectionChange"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" @search="events.search" @reset="events.reset" @toggle="events.toggleQuery" v-if="treeNode?.id" supportFold />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" v-if="!dragable" operationAuth="/base/#/meta-word/edit">新建</my-button>
              <my-button
                type="danger"
                @click="dragable = true"
                v-if="!(treeNode?.children && treeNode?.children.length) && !dragable"
                operationAuth="/base/#/meta-word/edit"
                >排序</my-button
              >
              <my-button type="info" @click="events.cancelSort" v-if="dragable" operationAuth="/base/#/meta-word/edit">取消</my-button>

              <my-button type="primary" @click="events.sort" v-if="dragable" operationAuth="/base/#/meta-word/edit">保存</my-button>
              <my-button type="danger" @click="events.deleteBatch" v-if="!dragable" operationAuth="/base/#/meta-word/edit" :disabled="!selectedIds.length">
                批量删除
              </my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #header>
        <div class="header">
          <span>{{ treeNode?.name }}</span>
        </div>
      </template>
    </table-page>
    <AddDialog ref="addRef" :treeNode="treeNode" @save-data="loadList" :columns="columns" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as metaWordApi from "@/api/meta-word";
import AddDialog from "./add.vue";
import useStore from "@/store";
import { assign, pick, keys } from "lodash";
const { $app, proxy } = useCtx();
const { word } = useStore();

const props = defineProps({
  treeNode: { type: Object, default: {} },
  treeData: { type: Array },
});
const dragable = ref(false);
const dragloading = ref(false);
const selectedIds = ref([]);
//列配置
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  {
    prop: "name",
    label: "名称",
    minwidth: 150,
  },
  // 文本可复制
  {
    prop: "code",
    label: "编码",
    withCopy: true,
    minwidth: 180,
  },
  {
    prop: "enabled",
    label: "启用状态",
    width: 90,
    custom: "switch",
    customRender: {
      attrs: {
        activeValue: true,
        inactiveValue: false,
        size: "small",
      },
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          if (record.enabled === true) {
            $app
              .$confirm({ title: "确定禁用？" })
              .then(() => {
                events.enableTable(record).then(() => {
                  resolve(true);
                });
              })
              .catch(() => {
                reject();
              });
          } else {
            $app
              .$confirm({ title: "确定启用？" })
              .then(() => {
                events.enableTable(record).then(() => {
                  resolve(true);
                });
              })
              .catch(() => {
                reject();
              });
          }
        });
      },
    },
  },

  {
    prop: "description",
    label: "备注",
    minwidth: 280,
  },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
]);
const sortTableData = ref<any>([]);
//查询面板
const query = ref<any>({});
const value1 = ref(false);
const defaultForm = {
  parentId: 0,
  name: "",
  code: "",
  enabled: "",
};
const queryItems = ref<any>({
  name: {
    type: "input",
    label: "名称",
    width: "140px",
    modelValue: "",
    attrs: {
      placeholder: "请输入名称",
    },
  },
  code: {
    type: "input",
    label: "编码",
    modelValue: "",
    width: "140px",
    attrs: {
      placeholder: "请输入编码",
    },
  },
  enabled: {
    modelValue: "",
    type: "select",
    label: "状态",
    width: "140px",
    options: [
      { value: false, label: "停用", type: "danger" },
      { value: true, label: "启用", type: "primary" },
    ],
    attrs: {
      placeholder: "请选择状态",
    },
  },

  description: {
    type: "input",
    label: "备注",
    modelValue: "",
    width: "140px",
    collapsed: true,
    attrs: {
      placeholder: "请输入备注",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = {
      ...data,
    };
    if (!props.treeNode.id) {
      resolve([]);
      return;
    }
    metaWordApi.getWordAll(props.treeNode.id, params, word.area).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [
  { type: "edit", label: "编辑" },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    events.edit(record);
  }
  if (type === "delete") {
    events.delete(record);
  }
};
const handleTableRowChange = (data: any[]) => {
  sortTableData.value = data;
};
const handleTableSelectionChange = (data: any[]) => {
  selectedIds.value = data.map((item) => item.id);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        metaWordApi.deleteWord([record.id], word.area).then(() => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
  deleteBatch: () => {
    $app
      .$deleteConfirm({
        title: `您确认要批量删除?`,
      })
      .then(() => {
        metaWordApi.deleteWord(selectedIds.value, word.area).then(() => {
          loadList();
          $app.$message.success(`批量删除成功`);
        });
      });
  },
  enableTree: () => {
    metaWordApi
      .editTree(props.treeNode.id, pick({ ...props.treeNode, enabled: !props.treeNode.enabled }, keys(assign({}, defaultForm))), word.area)
      .then(() => {
        emit("updateTree", props.treeNode.id);
        $app.$message.success(`${!props.treeNode.enabled ? "启用" : "停用"} ${props.treeNode.name} 成功`);
      });
  },
  enableTable: (record: any) => {
    const params = {
      code: record.code,
      description: record.description,
      enabled: !record.enabled,
      name: record.name,
    };
    return metaWordApi.editWord(record.id, params, word.area).then(() => {
      $app.$message.success("修改成功");
      loadList();
    });
  },
  sort: () => {
    dragloading.value = true;
    const params = {
      nodeId: props.treeNode.id,
      ids: sortTableData.value.map((item: any) => item.id),
    };
    metaWordApi.sortWord(params, word.area).then(() => {
      dragable.value = false;
      dragloading.value = false;
      loadList();
      $app.$message.success(`排序成功`);
    });
  },
  cancelSort: () => {
    dragable.value = false;
    loadList();
  },
  toggleQuery: () => {
    nextTick(() => {
      proxy.$refs.myWordTableRef?.mediaHeight();
    });
  },
});
const loadList = () => {
  proxy.$refs.myWordTableRef?.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "updateTree"]);
//接口暴露
defineExpose({
  loadList,
});
// watch监听
watch(
  () => props.treeNode,
  (val: any) => {
    if (!val) {
      return;
    }
    value1.value = val.enabled;
    loadList();
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.meta-dict-table {
  height: 100%;
  .header {
    span {
      margin-right: 20px;
    }
    span:first-child {
      font-weight: 550;
    }
    ::v-deep {
      .el-switch {
        height: 0px;
        margin-left: 10px;
      }
    }
  }
}
::v-deep {
  .t-query {
    flex: 1;
  }
}
</style>
