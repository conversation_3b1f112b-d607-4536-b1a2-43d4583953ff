<template>
  <my-drawer class="mock-add" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import * as metaWordApi from "@/api/meta-word";

import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import useStore from "@/store";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const { t } = useI18n();
const { word } = useStore();
const props = defineProps({
  treeNode: { type: Object, default: {} },
  columns: { type: Object },
});
const dialogTitle = computed(() => {
  return formType.value === "add" ? t("btn.new") : t("btn.edit");
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  nodeId: undefined,
  name: "",
  code: "",
  description: "",
  type: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      trigger: "blur",
      message: "请输入名称",
    },
  ],
  code: [{ required: true, message: "请输入", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 50,
      placeholder: "请输入名称",
    },
  },
  code: {
    label: "编码",
    type: "input",
    attrs: {
      maxlength: 500,
      placeholder: "请输入编码",
    },
    disabled: () => isUpdate.value,
  },
  description: {
    label: "备注",
    type: "textarea",
    attrs: {
      maxlength: 200,
      placeholder: "请输入备注",
    },
  },
});

const openDialog = async (type: string, row: any) => {
  console.log(type, row);
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 2. 加载相关的下拉列表数据
  // 3. 回显相关的操作
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      ruleForm.value = assign(defaultForm, { nodeId: props.treeNode.id });
    }
  });
};
const emits = defineEmits(["save-data"]);
const submit = (form: any) => {
  // 接口相关业务代码，执行完成后关闭弹窗
  if (isUpdate.value) {
    metaWordApi.editWord(form.id, form, word.area).then(() => {
      $app.$message.success("修改成功");
      emits("save-data");
      handleClose();
    });
  } else {
    metaWordApi.addWord(form, word.area).then(() => {
      $app.$message.success("新增成功");
      emits("save-data");
      handleClose();
    });
  }
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
