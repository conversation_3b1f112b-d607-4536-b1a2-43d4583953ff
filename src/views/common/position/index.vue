<template>
  <div class="posiiton-container" v-if="hasResultData && !loading">
    <div class="search">
      <div style="margin-right: 20px;">
        <span>查看全量排序结果: </span>
        <el-switch v-model="fullDocsEnabled" />
      </div>

      <div>
        <my-query :queryItems="queryItems" :refreshBtn="{ show: false }" @search="handleFilter" @reset="resetQuery" />
      </div>

      <div class="reset">
        <my-button @click="handleDefault" type="primary" v-if="hasResetCondition">重置</my-button>
      </div>
    </div>

    <div class="content">
      <div class="content-item" v-for="(traceLogItem, index) in resultData" :key="index">
        <div class="name">
          <span v-if="showMockSwitch(traceLogItem)">
            mock:<el-switch v-model="traceLogItem.mock" />
          </span>
          <span>{{ traceLogItem.name }}</span>
          <span>{{ getDocCountText(traceLogItem) }}</span>
        </div>

        <div class="docs" ref="scrollableElements">
          <div
            v-for="(doc, index1) in getDisplayDocs(traceLogItem)"
            :key="doc.doc.id || index1"
            class="list-card"
            :class="getCardClasses(doc, index1, traceLogItem)"
            @click="handleNodeToLine(doc, index)"
          >
            <div class="list-card-index">{{ index1 + 1 }}</div>
            <Card
              :data="doc.doc"
              :resultData="resultData"
              :index="index1"
              :showId="true"
              :keyEnum="keyEnum"
              :typeInfo="traceLogItem"
              :mode="routeQuery.mode"
              :markRecordId="routeQuery.markRecordId"
              :strategyId="routeQuery.strategyId"
              :targetId="routeQuery.targetId || targetId"
              :traceView="traceView"
              :extend-fields="routeQuery.extendFields"
              @onMarkSaved="handleMarkSaved"
            />
          </div>
          <my-empty :size="120" v-if="!hasDocsData(traceLogItem)" />
        </div>
      </div>
    </div>
  </div>
  <my-empty :size="120" v-if="!resultData" />
  <div v-loading="loading" v-if="loading" class="loading-position"></div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, computed, onUnmounted } from "vue";
import * as evaluationApi from "@/api/eval-evaluation";
import useCtx from "@/hooks/useCtx";
import { assign, cloneDeep, template } from "lodash";
import useStore from "@/store";
import Card from "./Card.vue";
import { ElMessage, ElLoading } from "element-plus";
import * as cepingApi from "@/api/eval-manage";
import * as util from "@/utils/common";
import { dataC } from "turing-plugin";

const { $router, proxy, $app } = useCtx();
const { api } = useStore();
const siteTableRef = ref();
const searchKey = ref("searchId");
const props = defineProps(["transformListData", "loadDataApi"]);
const activeUrl = ref($app.$route.query.searchId || $app.$route.query.url);
const routeQuery = ref($app.$route.query);
let timeout: any = "";
const loading = ref(false);
const targetId = ref("");
const fullDocsEnabled = ref<boolean>(false);
const resultData = ref<any[]>([]);

// Computed properties
const isCeping = computed(() => routeQuery.value.mode == "ceping");
const hasResultData = computed(() => resultData.value && resultData.value.length);
const hasResetCondition = computed(() => $app.$route.query?.searchId || $app.$route.query?.url);
const traceView = ref("recall");
const keyEnum = [
  { value: "_indexCode", label: "结果来源" },
  { value: "_indexName", label: "结果来源（中文）" },
  { value: "post_ts", label: "发布时间" },
  { value: "site", label: "站点" },
  { value: "indexField", label: "召回方式" },
  { value: "_textrecall_score", label: "影响重排的各因素得分详情" },
  { value: "id", label: "id" },
];
const queryItems = ref<any>({
  search: {
    type: "input",
    width: "240px",
    modelValue: "",
    attrs: {
      placeholder: "请输入标题搜索",
    },
  },
});
const handleFilter = (val: any) => {
  // 遍历每个召回组（如 ES向量召回-v2、ES文本召回-v2）
  for (const group of resultData.value) {
    // 遍历每组中的 docs
    const docs = group.docs || [];
    for (const docDto of docs) {
      // 精确匹配标题
      if (docDto.doc.title === val.search) {
        activeUrl.value = docDto.doc[searchKey.value]; // 找到则返回 URL
        break;
      }
    }
  }
  nextTick(() => {
    scrollToActiveListCard();
  });
};

const resetQuery = (obj: any) => {
  activeUrl.value = "";
  for (const key in obj) {
    queryItems.value[key].modelValue = obj[key].modelValue;
  }
};

// Helper methods
const isRankNode = (code: string): boolean => {
  return ["ai-prerank", "ai-rank", "ai-rerank"].includes(code);
};

const showMockSwitch = (traceLogItem: any): boolean => {
  return traceLogItem.mockDocs;
};

const getDocCountText = (traceLogItem: any): string => {
  const docsArray = traceLogItem?.[traceLogItem.mock ? "mockDocs" : "docs"] || [];
  const sizeField = traceLogItem.mock ? "mockDocSize" : "docSize";
  // 兼容之前没有docSize和mockDocSize的情况，没有时使用docs的长度
  const docSize = traceLogItem?.[sizeField] ?? docsArray.length;

  if (fullDocsEnabled.value && isRankNode(traceLogItem.code)) {
    const totalDocs = docsArray.length;
    return `(${docSize}/${totalDocs} 条)`;
  }
  return `(${docSize} 条)`;
};

const getDisplayDocs = (traceLogItem: any): any[] => {
  const docs = traceLogItem[traceLogItem.mock ? 'mockDocs' : 'docs'] || [];
  if (fullDocsEnabled.value) {
    return docs;
  }

  // 兼容之前没有docSize和mockDocSize的情况，没有时使用docs的长度
  const sizeField = traceLogItem.mock ? "mockDocSize" : "docSize";
  const docSize = traceLogItem?.[sizeField] ?? docs.length;
  return docs.slice(0, docSize);
};

const getCardClasses = (doc: any, index: number, traceLogItem: any): object => {
  // 兼容之前没有docSize和mockDocSize的情况，没有时使用docs的长度
  const docsArray = traceLogItem?.[traceLogItem.mock ? "mockDocs" : "docs"] || [];
  const sizeField = traceLogItem.mock ? "mockDocSize" : "docSize";
  const docSize = traceLogItem?.[sizeField] ?? docsArray.length;

  return {
    'active-list-card': activeUrl.value == doc.doc[searchKey.value],
    'yellow-card': fullDocsEnabled.value && index >= docSize
  };
};

const hasDocsData = (traceLogItem: any): boolean => {
  const docs = traceLogItem[traceLogItem.mock ? 'mockDocs' : 'docs'];
  return docs && docs.length > 0;
};
const handleNodeToLine = (data: any, index: number) => {
  ElMessage.closeAll();
  searchKey.value = data.doc.searchId ? "searchId" : "url";
  activeUrl.value = data.doc[searchKey.value];
  nextTick(() => {
    scrollToActiveListCard(index);
  });
};

const handleDefault = () => {
  const query = $app.$route.query;
  searchKey.value = query.searchId ? "searchId" : "url";
  activeUrl.value = query[searchKey.value] as string;
  nextTick(() => {
    scrollToActiveListCard();
  });
};
/**
 * 滚动到激活的列表卡片
 */
const scrollToActiveListCard = (columnIndex?: number) => {
  ElMessage.closeAll();
  let top = 0;
  const delayInterval = 1000; // 设置每个列滚动操作的延迟时间间隔（毫秒）
  const scrollableElements = proxy?.$refs?.scrollableElements as HTMLElement[];

  if (!scrollableElements) return;

  if (columnIndex !== undefined) {
    const clickCard = scrollableElements[columnIndex]?.querySelector(".active-list-card") as HTMLElement;
    const clickContainer = scrollableElements[columnIndex];
    if (clickCard && clickContainer) {
      const elementOffsetTop = clickCard.offsetTop;
      const containerScrollTop = clickContainer.scrollTop;
      top = elementOffsetTop - containerScrollTop;
    }
  }

  scrollableElements.forEach((container: HTMLElement, index: number) => {
    timeout = setTimeout(() => {
      const activeCard = container.querySelector(".active-list-card") as HTMLElement;
      if (index !== columnIndex && activeCard) {
        container.scrollTo({
          top: activeCard.offsetTop - container.offsetTop - top + 40,
          behavior: "smooth",
        });
      }
      if (!activeCard && resultData.value[index]) {
        ElMessage.closeAll();
        $app.$message.warning(resultData.value[index].name + "  没有查找到结果");
      }
    }, index * delayInterval);
  });
};

/**
 * 解析全链路结果
 *
 */
const transformListData = (res: any) => {
  return res.map((traceNode: any, columnIndex: number) => {
    (traceNode.docs || []).map((traceRecord: any, rowIndex: any) => {
      parseDoc(traceRecord, false, rowIndex, columnIndex);
    });

    //todo 解析mockdoc
    (traceNode.mockDocs || []).map((traceRecord: any, rowIndex: any) => {
      parseDoc(traceRecord, true, rowIndex, columnIndex);
    });

    return traceNode;
  });
};

/**
 *data {doc:{},markResult:{}}
 */
function parseDoc(data: any, isMock: boolean, rowIndex: any, columnIndex: any) {
  data.doc.url = data.doc.url || util.displayUrl(data.doc.protocol, data.doc.domain, data.doc.path);
  //用于检索
  data.doc.searchId = data.doc.id;
  data.doc.mock = isMock;
  data.doc.rowIndex = rowIndex;
  data.doc.columnIndex = columnIndex;
  if (isCeping.value) {
    data.doc.markResult = data.markResult ? data.markResult : {};
    data.doc._popoverVisible = false;
  }
}

const getTraceinfo = async (params?: any) => {
  const currentRouteQuery = $router.currentRoute.value.query;
  const strategyId = currentRouteQuery.strategyId;
  loading.value = true;
  resultData.value = [];

  try {
    const res: any = await props.loadDataApi(params);
    console.log("result:", res);
    console.log("routeQuery", currentRouteQuery);

    // 根据strategyId或tag过滤数据
    let filtedStrategy: any;
    if (strategyId) {
      console.log("走标注、任务模式");
      filtedStrategy = res.data.targets.find((strategy: any) => strategy.strategyId === strategyId);
      traceView.value = filtedStrategy.traceView;
      resultData.value = transformListData(filtedStrategy?.traceList || []);
    } else if (currentRouteQuery.tag) {
      // 体验模式
      console.log("体验模式");
      const tag = currentRouteQuery.tag;
      filtedStrategy = res.data.targets.find((strategy: any) => strategy.metadata?.tag === tag);
      resultData.value = transformListData(filtedStrategy?.traceList || []);
    } else {
      console.log("其他");
      filtedStrategy = res;
      resultData.value = transformListData(filtedStrategy?.trace || []);
    }

    targetId.value = filtedStrategy?.targetId || "";
    console.log("routeQuery.extendFields", currentRouteQuery.extendFields);

    if (activeUrl.value) {
      nextTick(() => {
        handleDefault();
      });
    }
  } catch (error) {
    console.error("获取全链路信息失败:", error);
  } finally {
    loading.value = false;
  }
};

/**
 * 处理标记保存回调
 * @param docId 当前doc的Id
 * @param columnIndex 当前组件的顺序
 */
const handleMarkSaved = (docId: string, columnIndex: number) => {
  console.log('标记保存回调 - docId:', docId, 'columnIndex:', columnIndex);

  // 重置markResult的默认值
  const resetMarkResult = () => ({
    value: "",
    remark: "",
    resultId: null
  });

  // 重置弹窗状态
  const resetPopoverState = (doc: any) => {
    doc._popoverVisible = false;
    doc._popoverzanVisible = false;
    doc._popovercaiVisible = false;
  };

  // 处理文档重置逻辑
  const resetDocMarkResult = (docs: any[]) => {
    docs.forEach((doc: any) => {
      if (doc.doc.id === docId && doc.doc.markResult) {
        doc.doc.markResult = resetMarkResult();
        resetPopoverState(doc.doc);
      }
    });
  };

  // 遍历resultData.value，在非columnIndex中找到doc.id == docId的项，重置其markResult
  resultData.value.forEach((item: any, index: number) => {
    if (index === columnIndex) return;

    if (!dataC.isEmpty(item.docs)) {
      resetDocMarkResult(item.docs);
    }

    // 同样处理mockDocs
    if (!dataC.isEmpty(item.mockDocs)) {
      resetDocMarkResult(item.mockDocs);
    }
  });
};

onMounted(() => {
  getTraceinfo();
});
onUnmounted(() => {
  clearTimeout(timeout);
});
//接口暴露
defineExpose({ getTraceinfo });
</script>
<style lang="scss" scoped>
.posiiton-container {
  overflow: auto;
  height: calc(100vh - 65px);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .search {
    display: flex;
    justify-content: flex-end;
    height: auto;
  }

  .arrow-to-right {
    width: 55px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    background: url("@/assets/images/arrowL.png") no-repeat;
    background-size: cover;
    background-position: center center;
  }

  .content {
    flex: 1;
    min-height: 0;
    display: flex;
    position: relative;
    overflow-x: auto;

    .content-item {
      flex: 1 0 330px;
      /* 简写：flex-grow | flex-shrink | flex-basis */
      min-width: 330px;
      /* 强制最小宽度 */
      height: 100%;
      display: flex;
      flex-direction: column;

      .name {
        height: 32px;
        margin-bottom: 10px;
        font-weight: bold;
        cursor: pointer;

        span:nth-child(2) {
          margin-left: 10px;
        }
      }

      .docs {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
        padding-left: 2px;
        overflow-y: auto;
        position: relative;

        .list-card {
          min-width: 220px;
          margin-bottom: 15px;
          width: calc(100% - 20px);

          padding: 10px 10px;
          box-shadow: var(--el-box-shadow-light);
          display: flex;

          .list-card-index {
            padding: 0 5px;
            font-weight: 800;
            margin-left: -10px;
          }
        }

        .active-list-card {
          background-color: #c4e4fc;
        }

        .yellow-card {
          background-color: rgba(255, 166, 0, 0.46);
        }
      }
    }
  }
}

.loading-position {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.position-dialog {
  .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
