<template>
  <page-wrapper route-name="analysis-plan::details::">
    <div class="idx-db-statistic">
      <el-card
        class="info-card"
        :style="{ height: activeCollapse == 1 ? '220px' : '60px' }"
      >
        <el-collapse
          v-model="activeCollapse"
          @change="events.collapseChange"
          class="collapse"
        >
          <el-collapse-item :name="1">
            <template #title>
              <span style="font-size: 16px; font-weight: 700">基本信息</span>
            </template>
            <el-descriptions column="3">
              <el-descriptions-item
                label="归因策略名称 : "
                label-class-name="bold"
                >{{
                  `${baseInfo.name}`
                }}</el-descriptions-item
              >
              <el-descriptions-item
                label="归因策略编码 : "
                label-class-name="bold"
                >{{
                 baseInfo.code
                }}</el-descriptions-item
              >
              <el-descriptions-item label="分组 : " label-class-name="bold">{{
                baseInfo.groupName
              }}</el-descriptions-item>
              <el-descriptions-item
                label="归因策略描述 : "
                label-class-name="bold"
                >{{
                  baseInfo.description
                }}
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-card
        class="table-card"
        :style="{
          height:
            activeCollapse == 1 ? 'calc(100vh - 370px)' : 'calc(100vh - 210px)',
        }"
      >
        <div class="el-descriptions">
          <div class="el-descriptions__header">
            <div class="el-descriptions__title">
              <span>版本信息</span>&nbsp;
              <el-button link type="primary" @click="events.refreshSite">
                <el-icon size="18">
                  <Refresh />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
        <div class="row">
          <el-tabs
            v-model="activeName"
            class="demo-tabs"
            @tab-click="handleClick"
          >
            <el-tab-pane label="对标goodcase分析" name="0"></el-tab-pane>
            <el-tab-pane label="自研badcase分析" name="1"></el-tab-pane>
          </el-tabs>

          <my-button
            type="add"
            @click="events.add"
            style="margin-left: 20px"
            >创建归因版本</my-button
          >
        </div>
        <div class="idx-db-statistic-site-table">
          <table-page
            ref="myTableRef"
            name="dataset-version-table"
            :columns="columns"
            :loadDataApi="loadListData"
            :transformListData="transformListData"
            :defaultSort="{ prop: 'version', order: 'desc' }"
            operationAuth="/base/#/dataset/edit"
            :operations="operations"
            @operation="handleOperation"
          >
          </table-page>
        </div>
      </el-card>
    </div>
    <Edit ref="addRef" :activeName="activeName" @save-data="loadList()"/>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import PlanTable from "./PlanTable.vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import Edit from "./edit.vue";
import * as evaluationApi from "@/api/eval-evaluation";
import * as util from "@/utils/common";
import { computed } from "vue";

const { $router, proxy, $app,$auth } = useCtx();
const { api } = useStore();
const testAuth = () => {
  return $auth.testAuth("/base/#/attribution-strategy/edit");
}
let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}(v${String(x.version).padStart(3, "0")})`;
    x.publishTsRender = timeC.format(x.publishTs, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
const tableData = ref([]);
const $id = $app.$route.query.strategyId as string;
const activeNameStr = $app.$route.query.activeName as string || "0";
let activeName = ref(activeNameStr);
const routeName = "analysis-plan";
//事件声明
const emit = defineEmits(["preview-data"]);
const activeCollapse = ref([1]);
let baseInfo = ref({})
let type = computed(() => $app.$route.query.mode);
let columns =ref( [
  {
    prop: "nameRender",
    label: "归因版本名称",
    width: 190,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.toFlow(record)
      },
    },
  },
  {
    prop: "description",
    label: "归因版本描述",
    minWidth: 300,
  },
  {
    label: "流程ID",
    prop: "processId",
    width: 250,
    withCopy:true
  },
  {
    label: "状态",
    width: 100,
    prop: "status",
    custom: "status",
    customRender: {
      options: {
        1: { type: "info", name: "草稿" },
        2: { type: "success", name: "已发布" },
      },
    },
  },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "operation", label: "操作", width: 230, fixed: "right" },
])

//列表查询
const loadListData = async (data) => {
  return new Promise((resolve: any) => {
    evaluationApi
      .getStrategyVersionList($app.$route.query.strategyId,activeName.value,data).then(result=>{
        resolve(result);
      })
      
  });
};
const getBaseInfo  = async ()=>{
   evaluationApi.getStrategyInfo($app.$route.query.strategyId).then((res)=>{
    baseInfo.value = res
   })
}
const handleClick=(val)=>{
  activeName.value = val.props.name
  loadList()
  $router.replace({
    path: $router.currentRoute.value.path,
    query: {
      strategyId: $id,
      metaLabel: metaLabel,
      activeName: activeName.value
    }
  });
}
//初始化
onMounted(() => {
  getBaseInfo();
});
const modelValue = reactive({
  idxDbInstData: {},
  dbCapacity: {
    mongo: 0,
    milvus: 0,
    manticore: 0,
  },
  datasetList: [],
  datasetVersionList: [],
  idxDbTemList: [],
  metaRegionList: [],
});
//操作
const operations = [
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => record.status == 2,
    disabledTips: "已发布，不可编辑",
  },
  {
    type: "publish",
    label: "发布",
    disabled: (record: any) => record.status == 2,
    disabledTips: "已发布，不可再次发布",
  },
  { type: "copy", label: "复制版本" },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  toFlow: (record: any) => {
    if ((record.status === 1) && $auth.testAuth('/base/#/attribution-strategy/edit')) {
      window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/detail??id=${$id}&&vid=${record.id}&t=strategy`);
    } else {
      window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/view?id=${$id}&&vid=${record.id}&t=strategy`);
    }
  },
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  refreshSite: () => {
    proxy.$refs["myTableRef"].loadData();
  },
  add: () => {
    proxy.$refs["addRef"].openWindow('add');
  },
  edit: (item) => {
    proxy.$refs["addRef"].openWindow('edit', item);
  },
  publish: (record: any) => {
    $app.$confirm({ title: `确定发布 ${record.nameRender} ?` }).then(() => {
      evaluationApi.publishStrategyVersion(record.id).then((result) => {
        loadList();
        $app.$message.success(`发布 ${record.nameRender} 成功`);
      });
    });
  },
  copy: (record: any) => {
    $app.$confirm({ title: `确定复制 ${record.nameRender}?` }).then(() => {
      evaluationApi.copyStrategyVersion(record.id).then((result) => {
        loadList();
        $app.$message.success(`复制 ${record.nameRender} 成功`);
      });
    });
  },
  delete: (record: any) => {
    $app.$confirm({ title: `确定删除 ${record.name}?` }).then(() => {
      evaluationApi.deleteStrategyVersion(record.id).then((result) => {
        loadList();
        $app.$message.success(`删除 ${record.name} 成功`);
      });
    });
  },
});
const loadList = () => {
  proxy.$refs["myTableRef"]?.loadData();
};
//接口暴露
defineExpose({
});
</script>
<style lang="scss">
.idx-db-statistic {
  padding: 10px;

  .info-card {
    .bold {
      font-weight: bold;
    }

    .collapse {
      border: none;
      .el-collapse-item__header {
        border: none;
        height: 23px;
        margin-bottom: 12px;
      }

      .el-collapse-item__wrap {
        border: none;
      }
    }

    .el-card__body {
      height: 100%;
      .el-collapse {
        height: 100%;
        .el-collapse-item {
          height: 100%;
          .el-collapse-item__wrap {
            height: 100%;
            .el-collapse-item__content {
              height: 100%;
              .el-descriptions {
                height: 100%;
                .el-descriptions__body {
                  height: 100%;
                  overflow-y: auto;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    margin-top: 10px;

    .el-card__body {
      height: 100%;
    }
  }
}
.idx-db-statistic-site-table {
  height: calc(100% - 80px);

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
    .el-table--fit{
      height: 100%!important;
    }
  }
}
</style>
<style lang="scss" scoped>
.row {
  display: flex;
}
::v-deep(.el-tabs) {
  margin-bottom: 10px;
  flex: 1;
}
</style>