<template>
  <my-drawer class="idx-db-inst-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form
      labelWidth="120px"
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
      @submit="submit"
    >
      <template #file>
        <my-upload
          ref="uploadRef"
          maxSize="10M"
          accept=".xls,.xlsx"
          v-model="ruleForm.file"
          drag
          drag-style
        >
          <my-button type="primary" plain>上传文件</my-button>
        </my-upload>
      </template>
    </my-form>
  </my-drawer>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import DedupConfig from "@/views/common/dedup/DedupConfig.vue";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as evaluationApi from "@/api/eval-evaluation";
import * as util from "@/utils/common";
import { CODE_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
const { $app, proxy } = useCtx();
const { api,common } = useStore();
const props = defineProps({
  groupEnum : { type: Array, default: [] }
});
//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增归因策略";
  if (formType.value == "edit") return "编辑归因策略";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  code: "",
  groupName: "",
  description: "",
};
const extraForm = {};
let ruleForm = ref<any>(assign({}, defaultForm, extraForm));
const { validateCodeRule,validateNameRule } = useValidate();
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur" ,validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, '请输入名称') },{ min: 2, max: 100, message: '长度需要在 2 到 100之间', trigger: 'blur' }],
  code: [{ required: true, trigger: "blur" ,validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, "编码不能为空") }],
  groupName: [{ required: true, message: "分组不能为空", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "归因策略名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入归因策略名称",
    },
  },
  code: {
    label: "归因策略编码",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入归因策略编码",
    },
    disabled: (record: any) => isUpdate.value,
  },
  groupName: {
    label: "分组",
    type: "select",
    options: computed(()=>props.groupEnum),
    attrs: {
      allowCreate: true,
    },
  },
  description: {
    label: "归因策略描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
      placeholder: "请输入归因策略描述",
    },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit" || type === "publish") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm, extraForm)));
    } else {
      ruleForm.value = assign({}, defaultForm, extraForm, row);
    }
  });
};
//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  if (isUpdate.value) {
    evaluationApi.modifyStrategy(form.id,form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    evaluationApi.insertStrategy(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>