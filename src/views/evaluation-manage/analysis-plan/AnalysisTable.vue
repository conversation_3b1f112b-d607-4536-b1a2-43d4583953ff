<template>
  <div class="analysis-plan-table">
    <table-page
      ref="myTableRef"
      name="analysis-plan-table"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/analysis-plan/edit"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" :disabled="!testAuth()">创建分析计划</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #process="scope">
        <!-- :status="scope.row?.fail?'warning':'success'" -->
        <el-popover placement="right" :width="480" status="warning" @show="updateSingle(scope.row.id)">
          <el-row :gutter="10">
            <el-col :span="12">
              <span>全部数量：{{ scope.row.processTotal }}</span>
            </el-col>
            <el-col :span="12">
            </el-col>
            <el-col :span="12">
              <span>成功数量：{{ scope.row.success }}</span>
            </el-col>
            <el-col :span="12">
              <span>失败数量: {{ scope.row?.fail }} </span>
            </el-col>
            <el-col :span="12">
              <span>开始时间：{{ scope.row.startTime }}</span>
            </el-col>
            <el-col :span="12">
              <span>结束时间：{{ scope.row.endTime }}</span>
            </el-col>
          </el-row>
          <el-progress
            :text-inside="true"
            :stroke-width="16"
            :percentage="getPercent(scope.row)"
            :status="statusEnum.find((item1) => item1.value == scope.row?.status)?.type"
          />
          <template #reference>
            <status-dot
              :type="statusEnum.find((item1) => item1.value == scope.row?.status)?.type"
              :name="statusEnum.find((item1) => item1.value == scope.row?.status)?.label"
            />
          </template>
        </el-popover>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { keys, assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as evaluationApi from "@/api/eval-evaluation";
import IntervalClient from "@/utils/interval-client";
import * as util from "@/utils/common";
const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/analysis-plan/edit");
};
const modeEnum = {
  0: "对标good",
  1: "自研bad",
};
const statusEnum = [
  { value: 0, label: "待分析", type: "info" },
  { value: 1, label: "正在分析", type: "warning" },
  { value: 2, label: "已完成", type: "success" },
  { value: 3, label: "已完成", type: "exception" },
];
const divideAndKeepTwoDecimals = (numerator: number, denominator: number) => {
  const result = numerator / denominator;
  return Math.floor(result * 10000) / 10000;
};
//获取百分比
const getPercent = (item: any) => {
  if (dataC.isEmpty(item)) {
    //item都为null返回0
    return 0;
  } else if ((item.success == 0 && item.processTotal == 0) || item.success >= item.processTotal) {
    //已结束状态下, 已处理和总量都为0 或者已处理大于等于总量
    return 100;
  } else {
    //total刚开始会没有值，直接给一亿
    const done = Number(!dataC.isEmpty(item.success) ? item.success + (item?.fail || 0) : 0);
    const total = Number(dataC.isEmpty(item.processTotal) || item.processTotal == 0 ? 100_000_000 : item.processTotal);
    const res = divideAndKeepTwoDecimals(done, total);
    return (res * 100).toFixed(2);
  }
  return 0;
};
const { api, common } = useStore();
const props = defineProps({
  groupEnum: { type: Array, default: [] },
});
//列配置
const columns = ref([
  {
    prop: "name",
    label: "计划名称",
    width: 180,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.statistic(record, "plan");
      },
    },
  },
  { prop: "description", label: "计划描述", width: 180 },
  {
    prop: "groupName",
    label: "分组",
    minWidth: 120,
  },
  {
    prop: "status",
    label: "分析进度",
    slotName: "process",
    width: 150,
    showOverflowTooltip: false,
    sortable: false,
  },
  {
    prop: "total",
    label: "doc条数",
    width: 120,
  },
  {
    prop: "mode",
    label: "归因模式",
    width: 150,
    custom: "status",
    customRender: {
      options: [
        { value: 0, label: "对标good" },
        { value: 1, label: "自研bad" },
      ],
    },
  },
  { prop: "processId", label: "归因策略流程ID", minWidth: 230 ,withCopy:true},
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "operation", label: "操作", width: 250, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  search: "",
});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    attrs: {
      placeholder: "名称 或 分组",
    },
  },
});

//列表查询
const loadListData = async (data: any) => {
  return new Promise((resolve: any) => {
    evaluationApi.getPlanPage(data).then((result) => {
      result.content = result.content.map((item) => ({
        ...item,
      }));
      getIntervalClinet(result.content);
      //返回数据
      resolve(result);
    });
  });
};
//定时任务监控
const intervalClinet = ref(null);
const updateSingle = (id) => {
  evaluationApi.getPlanProcess(id).then((res) => {
    let tableData = proxy.$refs["myTableRef"].getTableData();
    const list = tableData.map((x: any) => {
      if (x.id == id) {
        x.status = res.status;
        x.success = res.success;
        x.fail = res.fail;
        x.startTime = timeC.format(res.startTime, "YYYY-MM-DD hh:mm:ss");
        x.endTime = timeC.format(res.endTime, "YYYY-MM-DD hh:mm:ss");
        x.processTotal = res.total;
      }
      return x;
    });
    proxy.$refs["myTableRef"].setTableData(list);
  });
};
// 0：待分析  1：正在分析 2：已完成
const getIntervalClinet = (data) => {
  let tableData = data || proxy.$refs["myTableRef"].getTableData();
  //如果已有定时任务对象，则停止并创建新的
  intervalClinet.value?.disconnect();
  //如果列表为空 则不创建新的
  if (dataC.isEmpty(tableData)) return;
  // 获取定时任务对象并启动,以持续刷新任务信息
  intervalClinet.value = new IntervalClient(3000, true);
  intervalClinet.value.onHandler(getTaskProgressListByTask, tableData).connect();
};
const getTaskProgressListByTask = (tableData: Array<any>) => {
  const taskIdList = tableData.filter((x) => {
    return !dataC.isEmpty(x.id) && x.status == 1;
  });
  if (dataC.isEmpty(taskIdList)) return;
  taskIdList.map((item) => {
    updateSingle(item.id);
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => record.status !== 0,
    disabledTips: "已经开始分析的不可以编辑",
  },
  { type: "delete", label: "删除", btnType: "danger" },
  {
    type: "publish",
    label: "开始分析",
    disabled: (record: any) => record.status === 2,
    disabledTips: (record: any) => {
      if (record.status === 2) {
        return "有未完成的才可以开始分析";
      }
    },
  },
  {
    type: "result",
    label: "查找结果",
    //非执行中的 且为禁用状态可以构建
    disabled: (record: any) => record.status === 0,
    disabledTips: (record: any) => {
      return "没有开始分析的不可以查看结果";
    },
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  statistic: (record: any, mode: String) => {
    emit("statistic-data", record, mode);
  },
  result: (record: any, mode: String) => {
    emit("statistic-data", record);
  },
  add: () => {
    emit("edit-data-inst", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data-inst", "edit", record);
  },
  publish: (record: any) => {
    if (record.status == 1 || record.status == 3) {
      $app.$confirm({ title: `确定对其余未分析条数继续分析吗` }).then(() => {
        evaluationApi.getPlaTaskStart(record.id).then((res) => {
          $app.$message.success(`开始分析${record.name}`);
          updateSingle(record.id);
        });
      });
    } else {
      evaluationApi.getPlaTaskStart(record.id).then((res) => {
        $app.$message.success(`开始分析${record.name}`);
        updateSingle(record.id);
      });
    }
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        evaluationApi.deletePlanInfo(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
});
const modelValue = reactive({
  metaRegionList: [],
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//初始化
onMounted(() => {});
//销毁
onUnmounted(() => {
  intervalClinet.value?.disconnect();
});
//事件声明
const emit = defineEmits(["edit-data-inst", "statistic-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.analysis-plan-table {
  height: 100%;
}
</style>