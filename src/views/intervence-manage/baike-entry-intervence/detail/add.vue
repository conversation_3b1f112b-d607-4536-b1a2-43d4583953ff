<template>
  <my-drawer
    class="mock-add"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <my-form
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
      @submit="submit"
    >
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import * as entryIntervenceApi from "@/api/baike-entry-intervence";
import { getBucketList } from "@/api/data-asset";
import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import useStore from "@/store";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const { t } = useI18n();
const { word } = useStore();
const props = defineProps({
  currentTab: { type: String },
  columns: { type: Object },
  matchList: { type: Array },
  mustRecallList: { type: Array },
  statusList: { type: Array },
});
const dialogTitle = computed(() => {
  return formType.value === "add" ? t("btn.new") : t("btn.edit");
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

/* 校验 */
const { validateNameRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  url: "",
  title: "",
  entityType: "",
  rank: "",
  mustRecall: "",
  idStr: "",
  status: 0,
  idxCode: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  id: [
    {
      required: true,
      trigger: "change",
      message: "请输入词条id",
    },
  ],
  idxCode: [{ required: true, message: "请选择索引库", trigger: "blur" }],
  title: [{ required: true, message: "请输入词条名称", trigger: "change" }],
  entityType: [{ required: true, message: "请输入词条类型", trigger: "blur" }],
  url: [{ required: true, message: "请输入词条链接", trigger: "blur" }],
  rank: [{ required: true, message: "请输入rank值", trigger: "blur" }],
  mustRecall: [{ required: true, message: "请输入是否必出", trigger: "blur" }],
  status: [{ required: true, message: "请输入状态", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>();
const validateSeach = (id: any, idxCode: any) => {
  formRef.value.ruleFormRef.validateField(["id", "idxCode"], (err: any) => {
    if (err) {
      entryIntervenceApi.getDetailByDoc({
        docId: id,
        id: idxCode,
      }).then((res)=>{    
         ruleForm.value = assign({},ruleForm.value,res);
      })
    }
  });
};
const getOptions = async () => {
  const res = await getBucketList({ kindCodes: "IDX", enabled: true });
  const idxCodeOptions = res.data.map((item) => ({
    label: `${item.name}(${item.enName})`,
    value: item.code,
  }));
  formItems.value = {
    type: {
      modalValue: props.currentTab,
      label: "匹配类型",
      type: "select",
      options: props.matchList,
      disabled: () => true,
      attrs: { clearable: false },
      events: { change: () => {} },
    },
    id: {
      label: "词条id",
      type: "input",
      disabled: () => isUpdate.value,
      attrs: {
        maxlength: 50,
        placeholder: "请输入词条id和索引库，回车查询信息",
      },
      events: {
        change: (val: any) => {
          console.log(val);
          validateSeach(val, ruleForm.value.idxCode);
        },
      },
    },
    idxCode: {
      type: "select",
      label: "索引库",
      modelValue: "",
      options: idxCodeOptions,
      hidden: () => isUpdate.value,
      attrs: {
        clearable: true,
      },
      events: {
        change: (val: any) => {
          validateSeach(ruleForm.value.id, val);
        },
      },
    },
    title: {
      label: "词条名称",
      disabled: () => isUpdate.value,
      type: "input",
      attrs: {
        maxlength: 50,
        placeholder: "词条名称",
      },
    },
    entityType: {
      label: "词条类型",
      type: "input",
      disabled: () => isUpdate.value,
      attrs: {
        maxlength: 50,
        placeholder: "词条类型",
      },
    },
    url: {
      label: "词条链接",
      type: "input",
      disabled: () => isUpdate.value,
      attrs: {
        maxlength: 50,
        placeholder: "词条链接",
      },
    },
    rank: {
      label: "rank值",
      type: "inputNumber",
      disabled: () => isUpdate.value,
      attrs: {
        maxlength: 50,
        placeholder: "rank值",
      },
    },
    mustRecall: {
      label: "是否必出",
      type: "select",
      options: props.mustRecallList,
      attrs: { clearable: false },
      events: { change: () => {} },
    },
    status: {
      label: "状态",
      type: "radio",
      options: props.statusList,
    },
  };
};

const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 2. 加载相关的下拉列表数据
  getOptions();
  // 3. 回显相关的操作
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = assign(pick(row, keys(defaultForm)), {
        type: props.currentTab,
        entityType: row.type,
        id: row.idStr,
      }); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      
      ruleForm.value = assign({}, defaultForm, { type: props.currentTab });
    }
  });
};
const emits = defineEmits(["save-data"]);
const submit = (form: any) => {
  // 接口相关业务代码，执行完成后关闭弹窗
  if (isUpdate.value) {
    entryIntervenceApi
      .updateDetail({
        ...form,
        type: props.currentTab,
        id: form.idStr,
        masterId: $app.$route.query.id,
      })
      .then(() => {
        $app.$message.success("修改成功");
        emits("save-data");
        handleClose();
      });
  } else {
    entryIntervenceApi
      .insertDoc({
        ...form,
        matchType: props.currentTab,
        idStr: form.docId,
        type:form.entityType,
        masterId: $app.$route.query.id,
      })
      .then(() => {
        $app.$message.success("新增成功");
        emits("save-data");
        handleClose();
      });
  }
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
