<template>
  <page-wrapper route-name="baike-entry-intervence::">
    <div class="baike-entry-intervence">
      <MyTable
        ref="myTableRef"
        :columns="columns"
        :query="query"
        :loadDataApi="loadListData"
        :transformListData="transformListData"
        :operations="operations"
        @operation="handleOperation"
        :loadImmediately = true
        :withSort="false"
      >
        <template #query>
          <div class="flexBetweenStart">
            <my-query
              :queryItems="queryItems"
              :refreshBtn="{ show: true }"
              @search="events.search"
              @reset="events.reset"
            />
          </div>
        </template>
        <template #operate="{ row }">
          <my-button
            v-if="row.status === 0 || row.status === -1"
            link
            :type="row.status == '-1' ? 'primary' : 'danger'"
            operationAuth="/base/#/baike-entry-intervence/edit"
            @click="events.line(row)"
          >
            {{ row.status == "0" ? "下线" : row.status == "-1" ? "上线" : "" }}
          </my-button>
          <my-button
            link
            operationAuth="/base/#/baike-entry-intervence/edit"
            type="danger"
            :disabled="row.status == '1'"
            @click="events.lock(row)"
          >
            锁定
          </my-button>
        </template>
        <template #pagination>
          <!-- <div style="font-size: 16px; font-weight: 700; margin-right: 15px">
            <span>数据总量 : {{ util.formatNumber(0) }}</span>
            <span style="margin-left: 20px">筛选后数据总量 : {{ util.formatNumber(0) }}</span>
          </div> -->
      </template>
      </MyTable>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as entryIntervenceApi from "@/api/baike-entry-intervence";
import { getTemplateList } from "@/api/mock";
import { computed } from "vue";
import MyTable from "./MyTable.vue";

import * as util from "@/utils/common";
const { $app, proxy, $router } = useCtx();
const statusList = [
  { value: "0", label: "上线", type: "primary" },
  { value: "-1", label: "下线", type: "danger" },
  { value: "1", label: "锁定", type: "info" },
];
const routeName = "baike-entry-intervence";
//列配置
const columns = ref([
  {
    prop: "id",
    label: "实体词id",
    width: 225,
    withCopy: true,
  },
  {
    prop: "title",
    label: "实体词名称",
    minWidth: 180,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        openVersionPage(record);
      },
    },
  },
  {
    prop: "status",
    label: "状态",
    width: 100,
    custom: "status",
    customRender: {
      options: statusList,
    },
  },
  { prop: "operateTimeRender", label: "更新时间", width: 180 },
  { prop: "operateUser", label: "更新人", width: 120 },
  {
    prop: "operation",
    label: "操作",
    width: 110,
    fixed: "right",
    slotName: "operate",
  },
]);
//查询面板
const query = ref<any>({});
const queryItems = ref<any>({
  name: {
    type: "input",
    label: "实体词",
    modelValue: "",
    attrs: {
      placeholder: "实体词",
    },
  },
  status: {
    modelValue: [],
    type: "select",
    label: "状态",
    width: "180px",
    options: statusList,
    attrs: {
      placeholder: "状态",
    },
  },
  user: {
    type: "input",
    label: "操作人",
    modelValue: "",
    attrs: {
      placeholder: "操作人",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size, sort } = data;
  const params = {
    ...data,
  };
  return new Promise((resolve: any) => {
    entryIntervenceApi.getEntry(params).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.operateTimeRender = !dataC.isEmpty(x.operateTime)
      ? timeC.format(x.operateTime, "YYYY-MM-DD hh:mm:ss")
      : "";
    return x;
  });
};
//操作
const operations = computed(() => {
  return [
    { type: "line", label: "下线" },
    { type: "lock", label: "锁定", btnType: "danger" },
  ];
});
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "line") {
    events.line(record);
  } else if (type == "lock") {
    events.lock(record);
  }
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  line: (record: any) => {
    $app
      .$confirm({
        title: `您确认要${record.status == "0" ? "下线" : "上线"}${
          record.title
        }吗?`,
      })
      .then(() => {
        let statusParams;
        if (record.status == "-1") {
          statusParams = "0";
        } else if (record.status == 0) {
          statusParams = "-1";
        } else if (record.status == "1") {
          statusParams = "1";
        }
        entryIntervenceApi
          .updateStatus(record.id, { status: Number(statusParams) })
          .then(() => {
            $app.$message.success(
              `${record.status == "0" ? "下线" : "上线"}成功`
            );
            loadList();
          });
      });
  },
  lock: (record: any) => {
    $app
      .$confirm({
        title: `您确认要锁定${record.title}吗?`,
      })
      .then(() => {
        entryIntervenceApi.updateStatus(record.id, { status: 1 }).then(() => {
          $app.$message.success("锁定成功");
          loadList();
        });
      });
  },
});

const openVersionPage = (record: any) => {
  $router.push({
    name: `${routeName}::detail`,
    query: {
      id: record.id,
      metaLabel: [record.title],
    },
  });
};
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
}
</style>
