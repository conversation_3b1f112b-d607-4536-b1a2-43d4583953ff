<template>
  <el-card class="url-black-card">
    <div class="title">
      <my-button type="primary" link @click="events.direct(info.url)">
        {{ info.title }}
      </my-button>
      <my-button
        :type="info.isInBlackList ? 'info' : 'add'"
        @click="events.add"
        :disabled="info.isInBlackList"
        >加入黑名单</my-button
      >
    </div>
    <div class="_id">
      <span class="sub_title">docId：</span>{{ info.docId }}
    </div>
    <div class="content">
      <span class="sub_title">content：</span
      >{{ info.isShort ? info.short : info.content }}
      <my-button
        type="primary"
        link
        @click="info.isShort = !info.isShort"
        v-if="info.hasMore"
      >
        {{ info.isShort ? "更多" : "收起" }}
      </my-button>
    </div>
    <div class="info">
      <el-tag type="primary">{{
        info.postTs
          ? timeC.format(info.postTs * 1000, "YYYY-MM-DD hh:mm:ss")
          : "暂无时间"
      }}</el-tag>
      <el-tag type="success">{{ info.idxName }}</el-tag>
      <el-tag type="warning">{{ info.regionName }}</el-tag>
    </div>
    <URLBlackReason ref="addRef" @save-data="emit('save-data')" />
  </el-card>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as manualIntervention from "@/api/manual-intervention";
import URLBlackReason from "./URLBlackReason.vue";
import { computed } from "vue";
const { $app, proxy, $router } = useCtx();
const routeName = "manual-intervention";
const props = defineProps({
  index: { type: Number },
  info: { type: Object, default: {} },
  handleCollapse: { type: Function },
});

//事件列表
const events = reactive({
  direct: (url: string) => {
    window.open(url, "_blank");
  },
  log: (record: any) => {
    $router.push({
      name: `manual-intervention::domain-black-log`,
      query: {
        id: record.id,
        metaLabel: record.domain,
        regionCode: record.regionCode,
      },
    });
  },
  add: () => {
    proxy.$refs.addRef?.openDialog([props.info]);
  },
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({});
</script>
<style scoped lang="scss">
.url-black-card {
  line-height: 24px;
  margin-bottom: 5px;
  .title {
    display: flex;
    justify-content: space-between;
  }
  .sub_title {
    color: var(--el-color-primary-light-7);
  }
  .info {
    span {
      margin-right: 10px;
    }
  }
}
</style>
