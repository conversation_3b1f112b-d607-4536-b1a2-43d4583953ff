<template>
  <div class="baike-entry-intervence">
    <table-page
      ref="myTableRef"
      name="domainBlack"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/manual-intervention/edit"
      :withSort="false"
      :rowKey="null"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" operationAuth="/base/#/manual-intervention/edit">新建</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #operate="{ row }">
        <my-button link type="primary" :disabled="row.status == '1'" @click="events.edit(row)" operationAuth="/base/#/manual-intervention/edit">
          编辑
        </my-button>
        <my-button v-if="row.status !== '1'" link type="danger" :disabled="row.status == '1'" @click="events.log(row)"> 日志 </my-button>
        <my-button link :type="row.enabled ? 'danger' : 'primary'" @click="events.enable(row)" operationAuth="/base/#/manual-intervention/edit">
          {{ row.enabled ? "失效" : "生效" }}
        </my-button>
        <my-button link type="danger" @click="events.delete(row)" operationAuth="/base/#/manual-intervention/edit"> 删除 </my-button>
      </template>
    </table-page>
    <AddDialog ref="addRef" @save-data="loadList" :statusList="statusList" :areaList="queryItems.regionCodes.options" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as manualIntervention from "@/api/manual-intervention";
import { getBucketList } from "@/api/data-asset";
import { computed } from "vue";
import AddDialog from "./DomainBlackAdd.vue";
import { ITEM_RENDER_EVT } from "element-plus/es/components/virtual-list/src/defaults";
import { valueEquals } from "element-plus";
const { $app, proxy, $router } = useCtx();
import useStore from "@/store";
const { word, api } = useStore();
const routeName = "manual-intervention";
const props = defineProps({
  statusList: { type: Array },
  areaList: { type: Array },
  idxList: { type: Array },
});
//列配置
const columns = ref([
  {
    prop: "id",
    label: "id",
    width: 210,
    withCopy: true,
  },
  {
    prop: "domain",
    label: "domain",
    width: 180,
  },
  {
    prop: "site",
    label: "对应site",
    minWidth: 120,
  },
  {
    prop: "idx",
    label: "所属索引库",
    minWidth: 180,
  },
  {
    prop: "regionName",
    label: "所属环境",
    minWidth: 120,
  },
  {
    prop: "reason",
    label: "下线原因",
    minWidth: 150,
  },
  {
    prop: "enabled",
    label: "规则状态",
    minWidth: 90,
    custom: "status",
    customRender: {
      options: [
        { value: false, label: "失效", type: "danger" },
        { value: true, label: "生效", type: "primary" },
      ],
    },
  },
  {
    prop: "lastModifiedBy",
    label: "更新人",
    minWidth: 100,
  },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 120 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  {
    prop: "operation",
    label: "操作",
    width: 200,
    fixed: "right",
    slotName: "operate",
  },
]);
const idxCodeOptions = ref([]);
//查询面板
const query = ref<any>({
  regionCodes: ["hf"],
});
const queryItems = ref<any>({
  domain: {
    type: "input",
    label: "domain",
    width: 120,
    modelValue: "",
    attrs: {
      placeholder: "domain",
    },
  },

  site: {
    type: "input",
    label: "对应site",
    width: 120,
    modelValue: "",
    attrs: {
      placeholder: "对应site",
    },
  },
  idxCode: {
    label: "索引库",
    type: "select",
    options: computed(() => idxCodeOptions.value),
    attrs: {
      clearable: true,
    },
    events: { change: () => {} },
  },
  regionCodes: {
    type: "select",
    label: "环境",
    width: "180px",
    modelValue: ["hf"],
    options: [],
    attrs: {
      placeholder: "环境",
      multiple: true,
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size, sort } = data;
  const params = {
    ...data,
  };
  return new Promise((resolve: any) => {
    manualIntervention.getDomainBlack(params).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};

//事件列表
const events = reactive({
  log: (record: any) => {
    $router.push({
      name: `manual-intervention::domain-black-log`,
      query: {
        id: record.id,
        metaLabel: record.domain,
        regionCode: record.regionCode,
      },
    });
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.domain}?`,
      })
      .then(() => {
        manualIntervention.deleteDoaminBlackPage(record).then(() => {
          loadList();
          $app.$message.success(`删除 ${record.domain} 成功`);
        });
      });
  },
  enable: (record: any) => {
    $app
      .$confirm({
        title: `您确认要${record.enabled ? "失效" : "生效"}“${record.domain}”吗?`,
      })
      .then(() => {
        manualIntervention
          .updateDomainBlackStatus(record.id, {
            enabled: !record.enabled,
            regionCode: record.regionCode,
          })
          .then(() => {
            $app.$message.success(`${record.enabled ? "失效" : "生效"}成功`);
            loadList();
          });
      });
  },
});
const updateEnvirenment = () => {
  // columns.value.
  word.getAreaList().then((values: any) => {
    queryItems.value.regionCodes.options = values.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.code,
    }));
  });
};
updateEnvirenment();

const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};

onMounted(async () => {
  const res = await getBucketList({ kindCodes: "IDX", enabled: true });
  idxCodeOptions.value = res.data.map((item) => ({
    label: `${item.name}(${item.enName})`,
    value: item.code,
  }));
});

//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
  updateEnvirenment,
});
</script>
<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
}
</style>
