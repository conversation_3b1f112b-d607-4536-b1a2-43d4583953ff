<template>
  <my-dialog class="consistency-edit" v-model="dialogVisible" :title="dialogTitle" width="1400px"
    @confirm="handleConfirm" @close="handleClose">
    <div class="edit-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="basicFormItems" label-width="120px" />
      </div>

      <!-- 环境配置 -->
      <div class="env-config">
        <el-row :gutter="20">
          <!-- 环境A（基准环境） -->
          <el-col :span="12">
            <div class="env-section">
              <h3>环境A（基准环境）</h3>
              <my-form ref="formARef" :ruleForm="ruleForm.processConfigA" :formItems="envAFormItems"
                label-width="120px">

                <template #inputArgs>
                  <FlowParam :inputArgs="ruleForm.processConfigA.inputArgs" style="width: 100%"></FlowParam>
                </template>

                <!-- 环节配置插槽 -->
                <template #processConfigs>
                  <div class="process-config-list">
                    <div v-for="(config, index) in ruleForm.nodeMapping" :key="index" class="config-item">
                      
                      <el-row v-if="config.codeA !== 'FINAL_RECALL'" :gutter="10">
                        <el-col :span="8">
                          <el-select v-model="config.codeA" placeholder="环节" :disabled="isView" style="width: 100%">
                            <el-option v-for="item in concernNodeList" :key="item.id" :label="item.name"
                              :value="item.code" />
                          </el-select>
                        </el-col>
                        <el-col :span="12">
                          <el-select v-model="config.nodeIdA" placeholder="具体策略标签" :disabled="isView"
                            style="width: 100%" filterable>
                            <el-option v-for="item in filtedConcernNodeList(config.codeA, 'A')" :key="item.id"
                              :label="`${item.name} | ${item.parentName}`" :value="item.id" />
                          </el-select>
                        </el-col>
                        <el-col :span="4">
                          <el-button v-if="!isView" type="danger" :icon="Minus" circle size="small"
                            @click="removeProcessConfig(index)" />
                        </el-col>
                      </el-row>
                    </div>
                    <el-button v-if="!isView" type="primary" :icon="Plus" @click="addProcessConfig()"
                      style="margin-top: 10px">
                      添加
                    </el-button>
                  </div>
                </template>
              </my-form>
            </div>
          </el-col>

          <!-- 环境B（对比环境） -->
          <el-col :span="12">
            <div class="env-section">
              <h3>环境B（对比环境）</h3>
              <my-form ref="formBRef" :ruleForm="ruleForm.processConfigB" :formItems="envBFormItems"
                label-width="120px">

                <template #inputArgs>
                  <FlowParam :inputArgs="ruleForm.processConfigB.inputArgs" style="width: 100%"></FlowParam>
                </template>

                <!-- 环节配置插槽 -->
                <template #processConfigs>
                  <div class="process-config-list">
                    <div v-for="(config, index) in ruleForm.nodeMapping" :key="index" class="config-item">
                      <el-row v-if="config.codeA !== 'FINAL_RECALL'" :gutter="10">
                        <el-col :span="8">
                          <el-select v-model="config.codeB" placeholder="环节" :disabled="isView" style="width: 100%">
                            <el-option v-for="item in concernNodeList" :key="item.id" :label="item.name"
                              :value="item.code" />
                          </el-select>
                        </el-col>
                        <el-col :span="12">
                          <el-select v-model="config.nodeIdB" placeholder="具体策略标签" :disabled="isView"
                            style="width: 100%" filterable>
                            <el-option v-for="item in filtedConcernNodeList(config.codeB, 'B')" :key="item.id"
                              :label="`${item.name} | ${item.parentName}`" :value="item.id" />
                          </el-select>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                </template>
              </my-form>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </my-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Plus, Minus } from '@element-plus/icons-vue'
import type { FormRules } from 'element-plus'
import useCtx from '@/hooks/useCtx'
import * as evalTestApi from '@/api/eval-consistency-test'
import * as querySetApi from '@/api/eval-query-set'
import * as evalSetting from '@/api/eval-setting'
import * as sceneApi from '@/api/scene'
import * as commonApi from '@/api/common'
import * as util from '@/utils/common'
import FlowParam from "@/views/evaluation-manage/mark/FlowParam.vue";

const { $app } = useCtx()
const emit = defineEmits(['save-success'])

// 弹框状态
const dialogVisible = ref(false)
const mode = ref<'add' | 'edit' | 'view'>('add')
const currentRecord = ref<any>({})

// 表单引用
const formRef = ref()
const formARef = ref()
const formBRef = ref()

//关注节点列表
const concernNodeList = ref<any[]>([]);

//环节映射选项
const nodeMappingAOptions = ref<any[]>([])
const nodeMappingBOptions = ref<any[]>([])

// 是否为查看模式
const isView = computed(() => mode.value === 'view')

// 弹框标题
const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增任务',
    edit: '编辑任务',
    view: '查看任务'
  }
  return titleMap[mode.value]
})

// 表单数据
const ruleForm = ref<any>({
  id: '',
  name: '',
  queryGroupId: '',
  processConfigA: {
    region: '',
    processId: '',
    params: {}, //用于存储流程参数
    inputArgs: [], //用于前端展示的动态表单参数
  },
  processConfigB: {
    region: '',
    processId: '',
    params: {}, //用于存储流程参数
    inputArgs: [], //用于前端展示的动态表单参数
  },
  nodeMapping: []
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 50, message: '任务名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  queryGroupId: [
    { required: true, message: '请选择query集', trigger: 'change' }
  ]
}

// 选项数据
const queryGroupOptions = ref<any[]>([])
const regionOptions = ref<any[]>([])

// 场景策略列表
const sceneList = ref<any[]>([]);

const strategyOptions = ref<any[]>([])

// 基本信息表单项
const basicFormItems = ref<any>({
  name: {
    label: '任务名称',
    type: 'input',
    disabled: () => isView.value,
    attrs: {
      placeholder: '请输入任务名称',
      maxlength: 50
    }
  },
  queryGroupId: {
    label: 'query集',
    type: 'select',
    options: queryGroupOptions,
    disabled: () => isView.value,
    attrs: {
      placeholder: '请选择query集',
      filterable: true
    }
  }
})

// 环境A表单项
const envAFormItems = computed(() => ({
  region: {
    label: '基准环境',
    type: 'select',
    options: regionOptions.value,
    disabled: () => isView.value,
    attrs: {
      placeholder: '请选择基准环境',
      required: true
    }
  },
  processId: {
    label: '产品|场景版本',
    type: 'select',
    options: sceneList.value.map((item: any) => ({
      ...item,
      label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
      value: item.processId
    })),
    disabled: () => isView.value,
    attrs: {
      placeholder: '请选择场景策略版本',
      filterable: true,
      required: true
    },
    events: {
      change: (val: any) => {
        if (val) {
          // 当选择场景策略版本时，可以获取相关配置
          console.log('环境A选择场景策略:', val)
        }
      }
    }
  },

  inputArgs: {
    label: '流程参数',
    type: 'slot',
    slotName: 'inputArgs',
    hidden: () => !ruleForm.value.processConfigA.processId,
  },

  processConfigs: {
    label: '环节配置',
    type: 'slot',
    slotName: 'processConfigs'
  }
}))

// 环境B表单项
const envBFormItems = computed(() => ({
  region: {
    label: '对比环境',
    type: 'select',
    options: regionOptions.value,
    disabled: () => isView.value,
    attrs: {
      placeholder: '请选择对比环境',
      required: true,
    }
  },
  processId: {
    label: '产品|场景版本',
    type: 'select',
    options: sceneList.value.map((item: any) => ({
      ...item,
      label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
      value: item.processId
    })),
    disabled: () => isView.value,
    attrs: {
      placeholder: '请选择场景策略版本',
      filterable: true,
      required: true,
    },
    events: {
      change: (val: any) => {
        if (val) {
          // 当选择场景策略版本时，可以获取相关配置
          console.log('环境B选择场景策略:', val)
        }
      }
    }
  },

  inputArgs: {
    label: '流程参数',
    type: 'slot',
    slotName: 'inputArgs',
    hidden: () => !ruleForm.value.processConfigB.processId,
  },

  processConfigs: {
    label: '环节配置',
    type: 'slot',
    slotName: 'processConfigs'
  }
}))

// 初始化表单
const initForm = () => {
  ruleForm.value = {
    id: '',
    name: '',
    queryGroupId: '',
    processConfigA: {
      region: '',
      processId: '',
      processConfigs: [],
      params: {},
      inputArgs: []
    },
    processConfigB: {
      region: '',
      processId: '',
      processConfigs: [],
      params: {},
      inputArgs: []
    },
    nodeMapping: []
  }
}

// 添加环节配置
const addProcessConfig = () => {
  const config = {
    code: '',
    codeA: '',
    nodeIdA: '',
    parentNodeIdA: '',
    parentNodeNameA: '',

    codeB: '',
    nodeIdB: '',
    parentNodeIdB: '',
    parentNodeNameB: '',
  }

  ruleForm.value.nodeMapping.push(config)
}

// 删除环节配置
const removeProcessConfig = (index: number) => {
  ruleForm.value.nodeMapping.splice(index, 1);
}

/**
 * 
 * @param code 
 * @param env 
 */
const filtedConcernNodeList = (code: string, env: 'A' | 'B') => {
  if (env === 'A') {
    return nodeMappingAOptions.value.filter((item: any) => item.code === code);
  } else {
    return nodeMappingBOptions.value.filter((item: any) => item.code === code);
  }
}

// 加载query集选项
const loadQueryGroupOptions = async () => {
  try {
    querySetApi.publishList().then((res) => {
      queryGroupOptions.value = res.data.map((item: any) => ({
        value: item.id,
        label: item.name,
      }));
    });
  } catch (error: any) {
    console.error('加载query集失败:', error)
  }
}

// 加载环境选项
const loadRegionOptions = async () => {
  try {
    const res = await commonApi.getAreaEnum()
    regionOptions.value = (res.content || []).map((item: any) => ({
      label: item.name,
      value: item.code
    }))
  } catch (error: any) {
    console.error('加载环境选项失败:', error)
  }
}

// 加载场景策略版本列表
const loadSceneList = async () => {
  try {
    const res = await commonApi.getSceneVersion({});
    sceneList.value = res.data;
  } catch (error: any) {
    console.error('加载场景策略版本失败:', error)
  }
}

// 加载关注节点列表
const loadConcernNodeList = async () => {
  evalSetting.listConcernNode().then((res) => {
    concernNodeList.value = res.data;
  })
}

// 打开弹框
const open = (data: any) => {
  mode.value = data.mode
  currentRecord.value = data.data

  if (data.mode === 'add') {
    initForm()
  } else {
    // 编辑或查看模式，填充数据
    const detailData = data.data
    ruleForm.value = {
      ...detailData,
      processConfigA: {
        region: detailData.processConfigA?.region || '',
        processId: detailData.processConfigA?.processId || '',
        processConfigs: detailData.processConfigA?.processConfigs || [],
        params: detailData.processConfigA?.params || {},
        inputArgs: [] // 将由 watch 监听器根据 processId 和 params 自动填充
      },
      processConfigB: {
        region: detailData.processConfigB?.region || '',
        processId: detailData.processConfigB?.processId || '',
        processConfigs: detailData.processConfigB?.processConfigs || [],
        params: detailData.processConfigB?.params || {},
        inputArgs: [] // 将由 watch 监听器根据 processId 和 params 自动填充
      },
      nodeMapping: detailData.nodeMapping || []
    }
  }

  dialogVisible.value = true
  loadOptions()
}

// 关闭弹框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetForm()
}

// 确认保存
const handleConfirm = async () => {

  console.log("确认保存");
  

  if (isView.value) {
    handleClose()
    return
  }

  try {
    await formRef.value?.getValidateResult()

    const requestParams = {
      ...ruleForm.value,
      id: mode.value === 'edit' ? ruleForm.value.id : undefined
    }

    // 将 inputArgs 转换为 params 对象
    if (requestParams.processConfigA && requestParams.processConfigA.inputArgs) {
      requestParams.processConfigA.params = buildPayload(requestParams.processConfigA.inputArgs);
      // 删除 inputArgs，不需要提交到后端
      delete requestParams.processConfigA.inputArgs;
    }

    if (requestParams.processConfigB && requestParams.processConfigB.inputArgs) {
      requestParams.processConfigB.params = buildPayload(requestParams.processConfigB.inputArgs);
      // 删除 inputArgs，不需要提交到后端
      delete requestParams.processConfigB.inputArgs;
    }

    console.log('提交参数:', requestParams);

    // 完善nodeMapping 根据nodeIdA和nodeIdB完善A B节点信息
    ruleForm.value.nodeMapping.forEach((item: any) => {
      console.log("code", item.codeA);
      
      if(item.codeA === "FINAL_RECALL"){
        return;
      }

      const nodeA = nodeMappingAOptions.value.find((node: any) => node.id === item.nodeIdA);
      const nodeB = nodeMappingBOptions.value.find((node: any) => node.id === item.nodeIdB);

      item.codeA = nodeA.code;
      item.nodeNameA = nodeA.name;
      item.parentNodeIdA = nodeA.parentId || "";
      item.parentNodeNameA = nodeA.parentName || "";

      item.codeB = nodeB.code;
      item.nodeNameB = nodeB.name;
      item.parentNodeIdB = nodeB.parentId || "";
      item.parentNodeNameB = nodeB.parentName || "";
    })


    await evalTestApi.saveConsisMission(requestParams)

    $app.$message.success(mode.value === 'add' ? '创建成功' : '更新成功')
    emit('save-success')
    handleClose()
  } catch (error: any) {
    if (error.message) {
      $app.$message.error(error.message)
    }
  }
}

//同时监听 ruleForm.value.processConfigA.processId 与 ruleForm.value.processConfigB.processId 的变化
watch(
  () => [ruleForm.value.processConfigA.processId, ruleForm.value.processConfigB.processId],
  async (newVals) => {
    if (newVals[0]) {
      //更新流程参数，传入当前的params进行回显
      const currentParams = ruleForm.value.processConfigA.params || {};
      ruleForm.value.processConfigA.inputArgs = await getInputArgs(newVals[0], currentParams);
      //更新环节配置
      nodeMappingAOptions.value = await getConcernNodeList(newVals[0]);
    }
    if (newVals[1]) {
      //更新流程参数，传入当前的params进行回显
      const currentParams = ruleForm.value.processConfigB.params || {};
      ruleForm.value.processConfigB.inputArgs = await getInputArgs(newVals[1], currentParams);
      nodeMappingBOptions.value = await getConcernNodeList(newVals[1]);
    }
  },
  { immediate: true, deep: true }
);

const getConcernNodeList = async (processId: string) => {
  try {
    const res = await evalTestApi.concernNodeList(processId);
    return res.data;
  } catch (error: any) {
    console.error('加载关注节点失败:', error);
    return [];
  }
};


// 加载所有选项数据
const loadOptions = async () => {
  await Promise.all([
    loadQueryGroupOptions(),
    loadRegionOptions(),
    loadSceneList(),
    loadConcernNodeList(),
  ])

  // ruleForm.value.processConfigA.params = await getInputArgs("68c3d3973dd8794a9c38d349");
}

// 将 params 对象的值回显到 inputArgs 数组中
const fillBack = (from: Record<string, any>, to: Array<{ key: string; value: any }>) => {
  const result = to.map(item => ({
    ...item,
    value: from[item.key] !== undefined ? from[item.key] : item.value
  }));

  console.log("params回显到inputArgs", { from, to, result });

  return result;
}

// 将 inputArgs 数组转换为 params 对象
const buildPayload = (inputArgs: Array<{ key: string; value: any }>) => {
  if (!inputArgs || !Array.isArray(inputArgs)) {
    return {};
  }

  const payload: Record<string, any> = {};
  inputArgs.forEach((item) => {
    if (item && item.key && item.value !== undefined) {
      payload[item.key] = item.value;
    }
  });

  console.log("inputArgs转换为params", { inputArgs, payload });

  return payload;
}

//获取流程动态表单参数
const getInputArgs = async (processId: string, params?: Record<string, any>) => {
  try {
    const result: any = await commonApi.getProcessInputArgs(processId);
    if (!result?.inputArgs) {
      $app.$message.warning("未获取到流程参数，接口返回格式异常");
      return [];
    }
    const resp = result.inputArgs.filter((item: { key: string }) => item.key !== "query");

    // 如果有params，则回显值到inputArgs中
    if (params && Object.keys(params).length > 0) {
      return fillBack(params, resp);
    }

    console.log('获取到的流程参数:', resp)
    return resp;
  } catch (error) {
    console.error("Failed to get input args:", error);
    $app.$message.warning("获取流程参数失败");
    return [];
  }
}


// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.consistency-edit {
  .edit-content {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    /* 禁止横向滚动，去掉底部滚动条 */
    box-sizing: border-box;
    width: 100%;
  }

  .basic-info {
    margin-bottom: 10px;
    margin-top: 10px;
    padding-bottom: 20px;

    .el-form {
      display: flex;
    }
  }

  .flow-param-box {
    display: table;
  }

  .env-config {
    .env-section {
      border-radius: 4px;
      padding: 20px;
      background: #fafafa;

      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .process-config-list {
        width: 100%;

        .config-item {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
