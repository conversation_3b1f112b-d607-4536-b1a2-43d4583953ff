<template>
  <page-wrapper route-name="consistency-test::">
    <div class="consistency-test">
      <!-- 表格页面 -->
      <ConsistencyTable ref="tableRef" @edit="handleEdit" @view-result="handleViewResult" />

      <!-- 编辑弹框 -->
      <ConsistencyEdit ref="editRef" @save-success="handleSaveSuccess" />
    </div>
  </page-wrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import useCtx from '@/hooks/useCtx'
import useConsistencyStore from '@/store/consistency'
import ConsistencyTable from './table.vue'
import ConsistencyEdit from './edit.vue'


const { $app } = useCtx()
const consistency = useConsistencyStore()

const tableRef = ref()
const editRef = ref()

// 处理编辑操作
const handleEdit = (record: any) => {
  editRef.value?.open(record)
}

// 处理查看结果
const handleViewResult = (record: any) => {
  // 将完整的record数据存储到store中
  consistency.setCurrentRecord(record)

  // 只传递必要的路由参数
  $app.$router.push({
    name: 'consistency-test::detail',
    query: {
      id: record.id,
      metaLabel: [record.name],
    }
  })
}

// 处理保存成功
const handleSaveSuccess = () => {
  tableRef.value?.refresh()
}
</script>

<style lang="scss" scoped>
.consistency-test {
  height: 100%;
}
</style>