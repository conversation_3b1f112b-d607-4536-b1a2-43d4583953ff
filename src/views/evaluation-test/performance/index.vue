<template>
  <page-wrapper route-name="performance-test::">
    <div class="performance-test">
      <!-- 表格页面 -->
      <PerformanceTable ref="tableRef" @edit="handleEdit" @view-result="handleViewResult" />

      <!-- 编辑弹框 -->
      <PerformanceEdit ref="editRef" @save-success="handleSaveSuccess" />
    </div>
  </page-wrapper>
</template>

<script setup lang="ts">
import { ref } from "vue";
import useCtx from "@/hooks/useCtx";
import usePerformanceStore from "@/store/performance";
import PerformanceTable from "./table.vue";
import PerformanceEdit from "./edit.vue";

const { $app } = useCtx();
const performance = usePerformanceStore();

const tableRef = ref();
const editRef = ref();

// 处理编辑操作
const handleEdit = (type: string, row: any) => {
  editRef.value?.open(type, row);
};

// 处理查看结果
const handleViewResult = (record: any) => {
  // 将完整的record数据存储到store中
  performance.setCurrentRecord(record);

  // 只传递必要的路由参数
  $app.$router.push({
    name: "performance-test::detail",
    query: {
      id: record.id,
      metaLabel: [record.name],
    },
  });
};

// 处理保存成功
const handleSaveSuccess = () => {
  tableRef.value?.refresh();
};
</script>

<style lang="scss" scoped>
.performance-test {
  height: 100%;
}
</style>
