user root;
worker_processes 8;

events {
    worker_connections 2048;
}

http {
    gzip on;
    gzip_vary on;
    gzip_disable "msie6";
    gzip_proxied any;
    gzip_min_length 1024;
    gzip_comp_level 5;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript application/font-woff application/font-woff2 image/svg+xml image/x-icon application/x-javascript;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;

    sendfile off;
    server_tokens off;
    keepalive_timeout 65;
    client_max_body_size 500m;
    include mime.types;
    underscores_in_headers on;
    
    upstream skybox-gateway {
        server ${turing.skybox.gateway.host1};
        #server ${turing.skybox.gateway.host2};
        #server ${turing.skybox.gateway.host3};
        keepalive 200; 
    }

    upstream lynxiao-portal {
        server ${lynxiao-portal.host1};
        #server ${lynxiao-portal.host2};
        #server ${lynxiao-portal.host3};
        keepalive 200; 
    }

    upstream uap-server {
           server ${turing.uap-server.host};
    }

    upstream uap-manager {
         server ${turing.uap-mannager.host};
    }

    server {
        listen ${PORT};
        
        location = / {
            rewrite ^/$ /lynxiao/ ;
        }
        
        location = /lynxiao {
            rewrite ^/lynxiao$ /lynxiao/ ;
        }
        
         location /sso/ {
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             #skybox-gateway地址
             proxy_pass http://skybox-gateway/sso/;
        }
        
        location /lynxiao/sso/ {
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             #skybox-gateway地址
             proxy_pass http://skybox-gateway/sso/;
        }
        
        location / {
             root /usr/share/nginx/plugin_ui/;
             index index.html index.htm;
             try_files $uri $uri/ /lynxiao/index.html;

             # security handler interceptor done
             add_header Referer-Policy "origin";
             add_header X-Frame-Options "SAME-ORIGIN";
             add_header Content-Security-Policy "object-src 'self'";
             add_header X-Content-Type-Options "nos niff";
             add_header X-XSS-Protection "1; mode=block";
             add_header Strict-Transport-Security "max-age=63072000; includeSubdomains; preload";
             add_header X-Permitted-Cross-Domain-Policies "master-only";
             add_header X-Download-Options "noopen";
             add_header skynet-tlb-service-tag-selector "${flame.env.tag}";

             if ($request_filename ~* .*\.(htm|html)$) {
                 add_header Cache-Control "no-store";
             }
             if ($request_filename ~* .*\.(js|css|otf|woff2|svg)$) {
                 add_header Cache-Control "public, max-age=604800";
             }
             if ($request_uri ~* "\.zip$") {
                 return 403;
             }
        }
        
        location /conformance {
             root /usr/share/nginx/plugin_ui;
             index index.html;

             # security handler interceptor done
             add_header Referer-Policy "origin";
             add_header X-Frame-Options "SAME-ORIGIN";
             add_header Content-Security-Policy "object-src 'self'";
             add_header X-Content-Type-Options "nos niff";
             add_header X-XSS-Protection "1; mode=block";
             add_header Strict-Transport-Security "max-age=63072000; includeSubdomains; preload";
             add_header X-Permitted-Cross-Domain-Policies "master-only";
             add_header X-Download-Options "noopen";
             add_header skynet-tlb-service-tag-selector "${flame.env.tag}";

             if ($request_filename ~* .*\.(htm|html)$) {
                 add_header Cache-Control "no-store";
             }
             if ($request_filename ~* .*\.(js|css|otf|woff2|svg)$) {
                 add_header Cache-Control "public, max-age=604800";
             }
             if ($request_uri ~* "\.zip$") {
                 return 403;
             }
        }

        location /lynxiao/skybox/ {
             #skybox-gateway地址
             proxy_pass http://skybox-gateway/skybox/;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_http_version 1.1;
             proxy_read_timeout 600s;
             proxy_set_header Upgrade $http_upgrade;
             proxy_set_header Connection "upgrade";
             proxy_set_header skynet-tlb-service-tag-selector "${flame.env.tag}";
        }
         
      ####
        
        location /lynxiao/proxyApi/lynxiao-portal/flow/api/v1/canvas/process/call {
             #画布调试sse 直连
             proxy_pass http://lynxiao-portal/flow/api/v1/canvas/process/call;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_http_version 1.1;
             proxy_set_header Upgrade $http_upgrade;
             proxy_set_header Connection "upgrade";
             proxy_set_header skynet-tlb-service-tag-selector "${flame.env.tag}";
            chunked_transfer_encoding off;
            proxy_cache off;
        } 
        
        location /lynxiao/proxyApil/lynxiao-portal/eval/api/v1/experience/chat {
             proxy_pass http://lynxiao-portal/eval/api/v1/experience/chat;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_http_version 1.1;
             proxy_set_header Upgrade $http_upgrade;
             proxy_set_header Connection "upgrade";
             proxy_set_header skynet-tlb-service-tag-selector "${flame.env.tag}";
             proxy_read_timeout 300s;
             proxy_send_timeout 300s;
             chunked_transfer_encoding off;
             proxy_cache off;
        }
        
   
        
        location /lynxiao/proxyApi/ {
             #skybox-gateway地址
             proxy_pass http://skybox-gateway/;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_http_version 1.1;
             proxy_set_header Upgrade $http_upgrade;
             proxy_set_header Connection "upgrade";
             proxy_set_header skynet-tlb-service-tag-selector "${flame.env.tag}";
            chunked_transfer_encoding off;
            proxy_cache off;
        }
            
        location /uap-server/ {
             proxy_next_upstream error timeout;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             #uap-server地址
             proxy_pass http://uap-server/uap-server/;
        }

        location /uap-manager/ {
             proxy_next_upstream error timeout;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             #uap-manager地址
             proxy_pass http://uap-manager/uap-manager/;
        } 

        # Grafana 免密登录代理
        location /grafana/ {
            # 重写 URL，去掉 /grafana 前缀
            rewrite ^/grafana(/.*)?$ $1 break;

            # 设置免密登录的用户信息
            proxy_set_header X-WEBAUTH-USER "admin";
            proxy_set_header X-WEBAUTH-NAME "Admin User";
            proxy_set_header X-WEBAUTH-EMAIL "<EMAIL>";

            # 基本代理设置
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # WebSocket 支持
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_http_version 1.1;

            # 超时设置
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;

            # 代理到 Grafana
            proxy_pass http://**************:3000;

            # 禁用缓存
            proxy_cache off;
            proxy_buffering off;
        }

        # Grafana API 代理（用于 render-image 等 API 调用）
        location /grafana-api/ {
            # 重写 URL，去掉 /grafana-api 前缀
            rewrite ^/grafana-api(/.*)?$ $1 break;

            # API 调用使用 Bearer Token 认证
            set $grafana_token "glsa_vrwAo1hIRT4RevKcm3yIzHcbOE7ejF4J_794dde82";
            proxy_set_header Authorization "Bearer $grafana_token";

            # 基本代理设置
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 代理到 Grafana
            proxy_pass http://**************:3000;

            # 禁用缓存
            proxy_cache off;
        }
    }

    server {
        listen ${PORT1};
        
        location / {
             root /usr/share/nginx/plugin_ui/uap-manager-v3.3/;
             index index.html index.htm;
             try_files $uri $uri/ /index.html;

             if ($request_uri ~* "\.zip$") {
                 return 403;
             }
        }
        
        location /uap-server/ {
             proxy_next_upstream error timeout;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             #uap-server地址
             proxy_pass http://uap-server/uap-server/;
        }

        location /uap-manager/ {
             proxy_next_upstream error timeout;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             #uap-manager地址
             proxy_pass http://uap-manager/uap-manager/;
        }
    }
}