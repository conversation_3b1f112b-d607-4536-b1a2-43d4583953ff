<template>
  <page-wrapper route-name="consistency-test::details">
    <div class="container">
      <!-- 任务基本信息卡片 -->
      <div class="card">
        <div class="card-header">任务基本信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">任务名称</span>
            <span class="info-value">{{ modelValue.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间</span>
            <span class="info-value">{{ modelValue.createdDateRender }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">任务状态</span>
            <span class="info-value status"
              :class="statusEnum.find((item) => item.value === modelValue.status)?.type">{{
                statusEnum.find((item) => item.value === modelValue.status)?.label
              }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">压测路数</span>
            <span class="info-value">{{ modelValue.concurrency }}</span>
          </div>
        </div>
      </div>

      <!-- 整体性能指标卡片 -->
      <div class="card">
        <div class="card-header">整体性能指标</div>
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-label">90%耗时</div>
            <div class="metric-value metric-90th">{{ modelValue.p90 }}ms</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">QPS</div>
            <div class="metric-value metric-qps">{{ modelValue.qps }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">请求成功率</div>
            <div class="metric-value metric-success">{{ modelValue.successRate }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-label">错误数量</div>
            <div class="metric-value metric-error">{{ modelValue.errorCount || 0 }}</div>
          </div>
        </div>
      </div>

      <!-- 错误信息详情卡片 -->
      <div class="card" v-if="modelValue.errorCount > 0">
        <div class="card-header">错误信息详情</div>
        <div id="error-list">
          <div class="error-item" v-for="(item, index) in modelValue.errorDetailList">
            <div class="error-header">
              <span class="error-code">错误代码: {{ item.code }}</span>
              <span class="error-time">{{ item.time }}</span>
            </div>
            <div class="error-details">
              <div>TraceId: {{ item.traceId }}</div>
              <div>AppId: {{ item.appId }}</div>
              <div>错误信息: {{ item.msg }}</div>
            </div>
            <a class="view-details" @click="toggleErrorDetails(index)">查看完整错误详情</a>
            <div class="error-full-details" :id="`error-details-${index}`">
              {{ item.message }}
            </div>
          </div>
        </div>
      </div>

      <!-- 子服务性能数据卡片 -->
      <!-- <div class="card">
        <div class="chart-item">
          <div class="chart-title">各子服务性能数据</div>
          <div id="qps-chart" style="width: 100%; height: 520px">
            <iframe :src="modelValue.subServicesUrl" frameborder="0" style="width: 100%; height: 100%" />
          </div>
        </div>
      </div> -->

      <!-- QPS趋势卡片 -->
      <!-- <div class="card">
        <div class="chart-item">
          <div class="chart-title">QPS趋势</div>
          <div id="qps-chart" style="width: 100%; height: 520px">
            <iframe :src="modelValue.qpsUrl" frameborder="0" style="width: 100%; height: 100%" />
          </div>
        </div>
      </div> -->

      <!-- 相应趋势卡片 -->
      <!-- <div class="card">
        <div class="chart-item">
          <div class="chart-title">响应时间趋势</div>
          <div id="qps-chart" style="width: 100%; height: 520px">
            <iframe :src="modelValue.apiCostUrl" frameborder="0" style="width: 100%; height: 100%" />
          </div>
        </div>
      </div> -->

      <!-- Grafana 面板卡片 -->
      <!-- <div class="card">
        <div class="chart-item">
          <div class="chart-title">响应时间趋势</div>
          <div class="grafana-panel-container">
            <div v-if="grafanaLoading" class="loading">加载中...</div>
            <div v-else-if="grafanaError" class="error">{{ grafanaError }}</div>
            <div v-else ref="chartContainer" class="chart"></div>
          </div>
        </div>
      </div> -->

      <div>
        <button @click="loadPanel">加载 Panel</button>
        <img v-if="panelImage" :src="panelImage" />
        <p v-if="loading">加载中...</p>
        <p v-if="error" class="error">{{ error }}</p>
      </div>


    </div>
  </page-wrapper>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue";
import * as evalPerformanceTestApi from "@/api/eval-performance-test";
import usePerformanceStore from "@/store/performance";
import * as echarts from 'echarts';

const performance = usePerformanceStore();

// Grafana 面板相关变量
const chartContainer = ref(null)
const grafanaLoading = ref(false)
const grafanaError = ref(null)
let chart = null

// 状态枚举
const statusEnum = [
  { value: 0, label: "已创建", type: "info" },
  { value: 1, label: "运行中", type: "warning" },
  { value: 2, label: "已完成", type: "success" },
  { value: 3, label: "已终止", type: "danger" },
];

const modelValue = reactive({
  name: "",
  createdDateRender: "",
  status: "",
  concurrency: "",
  p90: "",
  qps: "",
  successRate: "",
  errorCount: "",
  errDetails: [],
  subServicesUrl: "",
  qpsUrl: "",
  apiCostUrl: "",
});

const toggleErrorDetails = (index: number) => {
  const detailsElement = document.getElementById(`error-details-${index}`);
  if (detailsElement && detailsElement.style.display === "block") {
    detailsElement.style.display = "none";
  } else if (detailsElement) {
    detailsElement.style.display = "block";
  }
};
const panelImage = ref('');
const loading = ref(false);
const error = ref('');

const loadPanel = async () => {
    loading.value = true;
    error.value = '';
    
    try {
        const response = await fetch('/lynxiao/proxyApi/lynxiao-eval/eval/api/v1/grafana/render-image?dashboardUid=xxx&panelId=1');
        const data = await response.json();
        
        if (data.data.success) {
            panelImage.value = `data:${data.data.contentType};base64,${data.data.content}`;
        } else {
            error.value = data.data.errorMessage;
        }
    } catch (err) {
        error.value = err.message;
    } finally {
        loading.value = false;
    }
}

onMounted(async () => {
  const curRecord = performance.getCurrentRecord();
  modelValue.name = curRecord.name;
  modelValue.createdDateRender = curRecord.createdDateRender;
  modelValue.status = curRecord.status;
  modelValue.concurrency = curRecord.concurrency;
  modelValue.p90 = curRecord.p90;
  modelValue.qps = curRecord.qps;
  modelValue.successRate = curRecord.successRate;
  modelValue.errorCount = curRecord.errorCount;
  const performanceResult = await evalPerformanceTestApi.getPerformanceResult(curRecord.id);
  modelValue.errDetails = performanceResult.data.errDetails || [];
  const defaultUrl = "http://**************:3000/d/eae7f756-73e8-4b1f-8456-3c6735e4d18e/e6b58b-e8af95?orgId=1&from=1761099166424&to=1761120766425&viewPanel=1";
  modelValue.subServicesUrl = performanceResult.data.subServicesUrl || defaultUrl;
  modelValue.qpsUrl = performanceResult.data.qpsUrl || defaultUrl;
  modelValue.apiCostUrl = performanceResult.data.apiCostUrl || defaultUrl;
});
</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
}

body {
  background-color: #f5f7fa;
  color: #333;
  padding: 20px;
  line-height: 1.6;
}

.container {
  padding: 16px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
}

.card+.card {
  margin-top: 16px;
}

.card-header {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  font-size: 14px;
}

.status.info {
  color: $info-color;
  background-color: $info-bg;
}

.status.warning {
  color: $warning-color;
  background-color: $warning-bg;
}

.status.success {
  color: $success-color;
  background-color: $success-bg;
}

.status.danger {
  color: $danger-color;
  background-color: $danger-bg;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin: 10px 0;
}

.metric-label {
  font-size: 14px;
  color: #666;
}

.metric-90th {
  color: #722ed1;
}

.metric-qps {
  color: #1890ff;
}

.metric-success {
  color: #52c41a;
}

.metric-error {
  color: #f5222d;
}

.error-item {
  border: 1px solid #ffe7e7;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fff2f0;
}

.error-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.error-code {
  color: #f5222d;
  font-weight: 600;
}

.error-time {
  color: #999;
  font-size: 14px;
}

.error-details {
  font-size: 14px;
  margin-bottom: 10px;
}

.view-details {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
  cursor: pointer;
}

.view-details:hover {
  text-decoration: underline;
}

.error-full-details {
  display: none;
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
}

.services-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.services-table th {
  background-color: #f8f9fa;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.services-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.services-table tr:hover {
  background-color: #f9f9f9;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 10px;
}

.chart-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  text-align: center;
}

// Grafana 面板样式
.grafana-panel-container {
  width: 100%;
  height: 520px;
}

.chart {
  width: 100%;
  height: 100%;
}

.loading,
.error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
}

.error {
  color: red;
}

@media (max-width: 768px) {

  .info-grid,
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .error-header {
    flex-direction: column;
  }

  .charts-container {
    grid-template-columns: 1fr;
  }

  .chart-item {
    height: 250px;
  }
}
</style>
