<template>
  <my-drawer class="performance-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800px">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems">
      <template #inputArgs>
        <ParamUse :inputArgs="ruleForm.inputArgs" style="width: 100%"></ParamUse>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import ParamUse from "@/views/common/paramComp/paramUse.vue";
import * as commonApi from "@/api/common";
import * as querySetApi from "@/api/eval-query-set";
import * as util from "@/utils/common";
import * as evalPerformanceTestApi from "@/api/eval-performance-test";

const { $app } = useCtx();

const dialogVisible = ref(false);
const formType = ref("add");
const record = ref<any>(null);
const formRef = ref();

const dialogTitle = computed(() => `${formType.value === "add" ? "新建" : "编辑"}性能测试`);
const isUpdate = computed(() => formType.value === "edit");

const defaultForm = {
  id: "",
  name: "",
  queryGroupId: "",
  region: "",
  concurrency: "1",
  runtime: "",
  round: "",
  processId: "",
  inputArgs: [], //用于前端展示的动态表单参数
};

const ruleForm = ref({ ...defaultForm });

const rules = {
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
};

const formItems = ref<any>({
  name: { label: "名称", type: "input", attrs: { maxlength: 255 }, disabled: isUpdate.value },
  queryGroupId: { label: "query集", type: "select", options: [] },
  region: { label: "环境", type: "select", options: [] },
  concurrency: { label: "压测路数", type: "inputNumber", attrs: { max: 20, min: 1 }, disabled: isUpdate.value },
  runtime: { label: "运行时间", type: "inputNumber", attrs: { max: 1440, min: 1 }, disabled: isUpdate.value },
  round: { label: "压测轮数", type: "inputNumber", attrs: { max: 100, min: 1 }, disabled: isUpdate.value },
  processId: {
    label: "流程",
    type: "select",
    options: [],
    events: {
      change: async (val: any) => {
        if (val) {
          ruleForm.value.inputArgs = await getInputArgs(val);
        }
      },
    },
  },
  inputArgs: {
    label: "流程参数",
    type: "slot",
    slotName: "inputArgs",
  },
});

const events = reactive({});

const open = async (type: string, row: any) => {
  formType.value = type;
  record.value = row;
  dialogVisible.value = true;

  await nextTick();
  formRef.value?.resetForm();
  ruleForm.value = type === "add" ? { ...defaultForm } : { ...defaultForm, ...row };
  ruleForm.value.processId = ruleForm.value.process?.processId || "";
  ruleForm.value.region = ruleForm.value.process?.region || "";
  if (ruleForm.value.processId !== "") {
    ruleForm.value.inputArgs = await getInputArgs(ruleForm.value.processId);
  }
};

const handleClose = () => {
  formRef.value?.resetForm();
  dialogVisible.value = false;
};

const handleConfirm = () => formRef.value.submitForm((valid) => valid && submit());

const submit = async () => {
  const params = isUpdate.value ? { ...record.value, ...ruleForm.value } : ruleForm.value;
  params.process = {
    processId: ruleForm.value.processId,
    region: ruleForm.value.region,
    params: buildPayload(ruleForm.value.inputArgs),
  };
  delete params.params;
  try {
    await evalPerformanceTestApi.savePerformanceMission(params);
    $app.$message.success(isUpdate.value ? "修改成功" : "新增成功");
    emit("save-success");
    handleClose();
  } catch {}
};

// 将 params 对象的值回显到 inputArgs 数组中
const fillBack = (from: Record<string, any>, to: Array<{ key: string; value: any }>) => {
  const result = to.map(item => ({
    ...item,
    value: from[item.key] !== undefined ? from[item.key] : item.value
  }));

  console.log("params回显到inputArgs", { from, to, result });

  return result;
}

// 将 inputArgs 数组转换为 params 对象
const buildPayload = (inputArgs: Array<{ key: string; value: any }>) => {
  if (!inputArgs || !Array.isArray(inputArgs)) {
    return {};
  }

  const payload: Record<string, any> = {};
  inputArgs.forEach((item) => {
    if (item && item.key && item.value !== undefined) {
      payload[item.key] = item.value;
    }
  });

  console.log("inputArgs转换为params", { inputArgs, payload });

  return payload;
};

//获取流程动态表单参数
const getInputArgs = async (processId: string, params?: Record<string, any>) => {
  try {
    const result: any = await commonApi.getProcessInputArgs(processId);
    if (!result?.inputArgs) {
      $app.$message.warning("未获取到流程参数，接口返回格式异常");
      return [];
    }
    const resp = result.inputArgs.filter((item: { key: string }) => item.key !== "query");

    // 如果有params，则回显值到inputArgs中
    if (params && Object.keys(params).length > 0) {
      return fillBack(params, resp);
    }

    console.log("获取到的流程参数:", resp);
    return resp;
  } catch (error) {
    console.error("Failed to get input args:", error);
    $app.$message.warning("获取流程参数失败");
    return [];
  }
};

onMounted(async () => {
  const res1 = await commonApi.getSceneVersion({});
  formItems.value.processId.options = res1.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
  const res2 = await querySetApi.publishList();
  formItems.value.queryGroupId.options = res2.data.map((item: any) => ({
    value: item.id,
    label: item.name,
  }));
  const res3 = await commonApi.getAreaEnum();
  formItems.value.region.options = res3.content.map((item: any) => ({
    label: item.name,
    value: item.code,
  }));
});

const emit = defineEmits(["save-success"]);
defineExpose({ open });
</script>

<style lang="scss" scoped>
.performance-edit {
}
</style>
