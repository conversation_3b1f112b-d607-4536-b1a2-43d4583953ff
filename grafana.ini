[server]
# 服务器配置
domain = 10.103.240.170
http_port = 3000
root_url = http://10.103.240.170:30009/grafana/
serve_from_sub_path = true

[security]
# 安全配置
allow_embedding = true
admin_password = admin123

[auth.proxy]
# 代理认证配置
enabled = true
header_name = X-WEBAUTH-USER
header_property = username
auto_sign_up = true
enable_login_token = false
headers = Name:X-WEBAUTH-NAME Email:X-WEBAUTH-EMAIL

[users]
# 用户配置
allow_sign_up = true
auto_assign_org = true
auto_assign_org_id = 1
auto_assign_org_role = Admin

[date_formats]
# 时区配置
default_timezone = Asia/Shanghai
